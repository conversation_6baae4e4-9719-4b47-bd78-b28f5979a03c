import { parse } from '@swc/core';

const code = `
const handleExport = (type: 'selected' | 'all') => {
  const exportParams = handleExportParams(formatedQueryParams);
  
  if (type === 'selected') {
    fireExport({
      exportParams: {
        bizType,
        jobName: 'NDR'
      }
    })
  } else {
    // 另一个分支
  }
}
`;

console.log('🔍 SWC AST 解析和位置验证');
console.log('=======================');

try {
  const ast = parse(code, {
    syntax: 'typescript',
    tsx: true,
    decorators: false,
  });

  console.log('📍 代码行数mapping:');
  const lines = code.split('\n');
  lines.forEach((line, index) => {
    if (line.trim()) {
      console.log(`L${index + 1}: ${line.trim()}`);
    }
  });

  // 查找if语句
  function findIfStatement(node: any): any {
    if (!node || typeof node !== 'object') return null;
    
    if (node.type === 'IfStatement') {
      return node;
    }
    
    for (const [key, value] of Object.entries(node)) {
      if (Array.isArray(value)) {
        for (const item of value) {
          const result = findIfStatement(item);
          if (result) return result;
        }
      } else if (value && typeof value === 'object') {
        const result = findIfStatement(value);
        if (result) return result;
      }
    }
    
    return null;
  }

  const ifNode = findIfStatement(ast);
  
  if (ifNode && ifNode.span) {
    console.log('\n🎯 If语句 span 信息:');
    console.log(`Start: ${ifNode.span.start}`);
    console.log(`End: ${ifNode.span.end}`);
    
    // 验证span位置
    const actualCharAtStart = code[ifNode.span.start];
    console.log(`实际字符: "${actualCharAtStart}"`);
    
    // 计算行号
    let line = 1;
    let column = 1;
    for (let i = 0; i < ifNode.span.start; i++) {
      if (code[i] === '\n') {
        line++;
        column = 1;
      } else {
        column++;
      }
    }
    
    console.log(`计算的行号: L${line}:C${column}`);
    
    // 验证这一行是否包含if
    const targetLine = lines[line - 1];
    console.log(`目标行内容: "${targetLine}"`);
    console.log(`包含if关键字: ${targetLine?.includes('if') ? '✅' : '❌'}`);
    
  } else {
    console.log('❌ 未找到if语句节点');
  }

} catch (error) {
  console.error('解析错误:', error);
}