import { analyzeCode } from './src/index.js';

// 精确模拟用户报告的问题结构
const problematicCode = `
const handleExport = (type: 'selected' | 'all') => {
  const exportParams = handleExportParams(formatedQueryParams);
  
  if (type === 'selected') {
    fireExport({
      exportParams: {
        bizType,
        jobName: 'NDR'
      }
    })
  } else {
    // 另一个分支
  }
}
`;

console.log('🔍 详细诊断位置映射问题');
console.log('========================');

try {
  const result = await analyzeCode(problematicCode, 'test.tsx', {
    details: true,
    outputType: 'text',
    debug: true,
    enableDebugLog: true
  });
  
  console.log('\n📊 分析结果:');
  console.log(`总复杂度: ${result.complexity}`);
  
  if (result.functions && result.functions[0]) {
    const func = result.functions[0];
    console.log(`\n🔧 函数: ${func.name}`);
    console.log(`复杂度: ${func.complexity}`);
    console.log(`位置: L${func.line}:C${func.column}`);
    
    if (func.details && func.details.length > 0) {
      console.log('\n📋 详细步骤:');
      func.details.forEach((detail, index) => {
        console.log(`${index + 1}. L${detail.line}:C${detail.column} - ${detail.description} (+${detail.increment})`);
        if (detail.context) {
          console.log(`   上下文: ${detail.context}`);
        }
      });
      
      // 分析位置映射问题
      console.log('\n🎯 位置映射分析:');
      func.details.forEach((detail, index) => {
        const lineNumber = detail.line;
        const lines = problematicCode.split('\n');
        if (lineNumber > 0 && lineNumber <= lines.length) {
          const actualLine = lines[lineNumber - 1];
          console.log(`步骤${index + 1}: L${lineNumber} → "${actualLine?.trim()}"`);
          console.log(`   描述: ${detail.description}`);
          console.log(`   应该包含if关键字: ${actualLine?.includes('if') ? '✅' : '❌'}`);
          console.log(`   包含fireExport: ${actualLine?.includes('fireExport') ? '⚠️' : '✅'}`);
          console.log(`   包含bizType: ${actualLine?.includes('bizType') ? '⚠️' : '✅'}`);
        }
      });
    } else {
      console.log('❌ 没有详细步骤信息');
    }
  }
  
} catch (error) {
  console.error('❌ 错误:', error);
}