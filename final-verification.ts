import { analyzeCode } from './src/index';

// 原始问题中的确切代码
const originalProblemCode = `
fireExport({
  exportParams: {
    bizType,
    jobName: 'NDR',
  }
});
`;

async function testOriginalIssue() {
  console.log('🎯 测试原始问题是否已修复...\n');
  console.log('测试代码:');
  console.log(originalProblemCode);
  
  try {
    const result = await analyzeCode(originalProblemCode, 'original-issue.tsx', {
      enableDebugLog: false,
      includeDetails: true,
      enableShowContext: true
    });
    
    console.log('\n📊 分析结果:');
    console.log(`总复杂度: ${result.complexity}`);
    console.log(`函数数量: ${result.functions.length}`);
    
    if (result.functions.length > 0) {
      result.functions.forEach(func => {
        console.log(`\n🔧 函数: ${func.name}`);
        console.log(`  复杂度: ${func.complexity}`);
        
        if (func.details && func.details.length > 0) {
          console.log('  详细步骤:');
          func.details.forEach((detail, index) => {
            console.log(`    ${index + 1}. +${detail.increment} - ${detail.description}`);
          });
        }
      });
    }
    
    console.log('\n🎯 最终验证:');
    if (result.complexity === 0) {
      console.log('✅ 原始问题已修复! fireExport({ 和 bizType, 不再被误判为条件语句');
      console.log('✅ span定位错误已解决');
    } else {
      console.log('❌ 问题未完全解决，仍有误判的复杂度');
    }
    
  } catch (error) {
    console.error('❌ 分析失败:', error);
  }
}

testOriginalIssue();