/**
 * 测试多字节字符列宽计算修复
 * 验证中文字符的视觉列位置计算是否正确
 */

import { analyzeCode } from './src/index';

const testCodeWithChinese = `
function 处理数据() {
  const 用户名 = "张三"; // 中文变量名
  if (用户名 === "李四") {
    console.log("匹配用户：" + 用户名);
    return true;
  }
  return false;
}

// 混合中英文的复杂情况
const config = {
  title: "系统配置",
  description: "这是一个包含中文的配置项",
  maxLength: 100
};
`;

async function testMultiByteCharacterSupport() {
  console.log('🔧 测试多字节字符列宽计算修复...\n');
  
  try {
    const result = await analyzeCode(testCodeWithChinese, 'test-chinese.ts', {
      enableDetails: true,
      enableDebugLog: false // 关闭调试输出以便查看结果
    });

    console.log('📊 分析结果:');
    console.log(`总复杂度: ${result.complexity}`);
    console.log(`函数数量: ${result.functions.length}`);
    
    result.functions.forEach((fn, index) => {
      console.log(`\n函数 ${index + 1}: ${fn.name}`);
      console.log(`  复杂度: ${fn.complexity}`);
      console.log(`  位置: L${fn.line}:C${fn.column}`);
      
      if (fn.details && fn.details.length > 0) {
        console.log('  详细步骤:');
        fn.details.forEach((step, stepIndex) => {
          console.log(`    ${stepIndex + 1}. L${step.line}:C${step.column}: +${step.increment} - ${step.description} [${step.ruleId}]`);
        });
      }
    });

    // 测试字符宽度计算功能
    console.log('\n🧮 字符宽度计算测试:');
    const testStrings = [
      'hello',           // 纯英文 - 应该是5
      '你好',            // 纯中文 - 应该是4 (2+2)
      'hello你好',       // 混合 - 应该是9 (5+4)
      'const 用户名 = "张三";', // 代码混合 - 应该是较大数值
    ];

    // 导入 PositionConverter 来测试字符宽度计算
    const { PositionConverter } = await import('./src/utils/position-converter');
    
    testStrings.forEach(str => {
      const visualWidth = PositionConverter.calculateStringVisualWidth(str);
      console.log(`  "${str}" -> 视觉宽度: ${visualWidth}`);
    });

    console.log('\n✅ 第三阶段修复完成: 多字节字符列宽计算已优化');
    
  } catch (error) {
    console.error('测试失败:', error);
  }
}

testMultiByteCharacterSupport();