/**
 * 🔍 全链路分析 - Span处理链路诊断
 * 参考: codebase/span-logic-chains-analysis.md
 */

import { analyzeCode } from './src/index';
import { parseSync } from '@swc/core';
import * as fs from 'fs';

async function fullChainAnalysis() {
  console.log('🔍 全链路分析 - Span处理链路诊断\n');
  
  try {
    const filePath = '/Users/<USER>/projj/git.imile.com/ux/ds-web/apps/ds/src/pages/InventoryEE/NDR/index.tsx';
    const sourceCode = fs.readFileSync(filePath, 'utf-8');
    
    console.log('=== 第一步：AST解析分析 ===');
    const ast = parseSync(sourceCode, {
      syntax: 'typescript',
      tsx: true,
      comments: false,
    });
    
    // 查找所有真正的IfStatement节点
    const realIfStatements: any[] = [];
    const suspiciousNodes: any[] = [];
    
    function visitNode(node: any, path: string = 'root') {
      if (!node || typeof node !== 'object') return;
      
      if (node.type === 'IfStatement') {
        const spanStart = node.span?.start;
        const actualContent = sourceCode.slice(spanStart, Math.min(spanStart + 50, sourceCode.length));
        
        if (actualContent.trim().startsWith('if')) {
          realIfStatements.push({
            span: node.span,
            path,
            content: actualContent,
            test: node.test
          });
        } else {
          suspiciousNodes.push({
            span: node.span,
            path,
            content: actualContent,
            test: node.test
          });
        }
      }
      
      // 递归访问子节点
      for (const [key, value] of Object.entries(node)) {
        if (Array.isArray(value)) {
          value.forEach((child, index) => {
            visitNode(child, `${path}.${key}[${index}]`);
          });
        } else if (value && typeof value === 'object') {
          visitNode(value, `${path}.${key}`);
        }
      }
    }
    
    visitNode(ast);
    
    console.log(`📊 AST解析结果:`);
    console.log(`- 真正的If语句: ${realIfStatements.length} 个`);
    console.log(`- 可疑的节点: ${suspiciousNodes.length} 个\n`);
    
    console.log('🎯 真正的If语句:');
    realIfStatements.forEach((stmt, index) => {
      console.log(`${index + 1}. Span ${stmt.span.start}-${stmt.span.end}`);
      console.log(`   内容: "${stmt.content.replace(/\n/g, '\\n')}"`);
      if (stmt.test && stmt.test.span) {
        const testContent = sourceCode.slice(stmt.test.span.start, stmt.test.span.end);
        console.log(`   条件: "${testContent}"`);
      }
      console.log();
    });
    
    console.log('🚨 可疑节点:');
    suspiciousNodes.forEach((stmt, index) => {
      console.log(`${index + 1}. Span ${stmt.span.start}-${stmt.span.end}`);
      console.log(`   内容: "${stmt.content.replace(/\n/g, '\\n')}"`);
      if (stmt.test && stmt.test.span) {
        const testContent = sourceCode.slice(stmt.test.span.start, stmt.test.span.end);
        console.log(`   条件: "${testContent}"`);
      }
      console.log();
    });
    
    console.log('=== 第二步：复杂度分析链路测试 ===');
    const result = await analyzeCode(sourceCode, filePath, {
      enableDetails: true,
      enableDebugLog: true
    });
    
    console.log('\n📊 当前分析结果:');
    result.functions.forEach((fn) => {
      console.log(`\n🔧 ${fn.name} (复杂度: ${fn.complexity})`);
      if (fn.details) {
        fn.details.forEach((step, index) => {
          console.log(`  ${index + 1}. L${step.line}:C${step.column} - ${step.description} (+${step.increment})`);
          console.log(`     ruleId: ${step.ruleId}`);
          if (step.span) {
            console.log(`     span: ${step.span.start}-${step.span.end}`);
          }
        });
      }
    });
    
    console.log('\n=== 第三步：链路问题诊断 ===');
    
    // 检查是否有真正的if语句被遗漏
    const detectedIfComplexity = result.functions.reduce((total, fn) => {
      return total + (fn.details?.filter(step => 
        step.ruleId === 'if-statement' || step.description.includes('条件语句')
      ).length || 0);
    }, 0);
    
    console.log(`🔍 诊断结果:`);
    console.log(`- AST中真正的If语句: ${realIfStatements.length} 个`);
    console.log(`- 复杂度分析检测到的If语句: ${detectedIfComplexity} 个`);
    console.log(`- 可疑的伪If节点: ${suspiciousNodes.length} 个`);
    
    if (realIfStatements.length > detectedIfComplexity) {
      console.log('\n⚠️  警告: 有真正的if语句被过滤掉了！');
      console.log('这说明isValidIfStatement验证过度严格。');
      
      // 分析被过滤的if语句
      console.log('\n🔧 被过滤的If语句分析:');
      realIfStatements.forEach((stmt, index) => {
        console.log(`${index + 1}. Span ${stmt.span.start}: "${stmt.content}"`);
        
        // 模拟验证逻辑
        const testContent = stmt.test && stmt.test.span ? 
          sourceCode.slice(stmt.test.span.start, stmt.test.span.end) : '';
        
        console.log(`   测试条件: "${testContent}"`);
        
        // 检查是否会被当前验证逻辑过滤
        const suspiciousPatterns = [
          /^\s*[a-zA-Z_$][a-zA-Z0-9_$]*\s*:/, // 对象属性模式 "key:"
          /^[^()]*:/, // 简单的冒号检查
          /^[^()]*\{/, // 对象字面量开始
        ];
        
        let wouldBeFiltered = false;
        for (const pattern of suspiciousPatterns) {
          if (pattern.test(testContent)) {
            console.log(`   ❌ 会被过滤 - 匹配模式: ${pattern}`);
            wouldBeFiltered = true;
            break;
          }
        }
        
        if (!wouldBeFiltered) {
          console.log(`   ✅ 不应该被过滤`);
        }
        console.log();
      });
    }
    
  } catch (error) {
    console.error('全链路分析失败:', error);
  }
}

fullChainAnalysis();