/**
 * 专门调试IfStatement验证过程
 */

import { analyzeCode } from './src/index';
import { parseSync } from '@swc/core';
import * as fs from 'fs';

async function debugIfStatementValidation() {
  console.log('🔍 调试IfStatement验证过程\n');
  
  try {
    const filePath = '/Users/<USER>/projj/git.imile.com/ux/ds-web/apps/ds/src/pages/InventoryEE/NDR/index.tsx';
    const sourceCode = fs.readFileSync(filePath, 'utf-8');
    
    console.log('=== 第一步：查找真正的if语句 ===');
    
    const lines = sourceCode.split('\n');
    const realIfLines: Array<{lineNum: number, line: string, offset: number}> = [];
    
    let currentOffset = 0;
    lines.forEach((line, index) => {
      if (line.trim().startsWith('if ') || line.includes(' if ')) {
        realIfLines.push({
          lineNum: index + 1,
          line: line.trim(),
          offset: currentOffset + line.indexOf('if')
        });
      }
      currentOffset += line.length + 1; // +1 for newline
    });
    
    console.log(`📝 在源码中找到 ${realIfLines.length} 个疑似if语句:`);
    realIfLines.forEach((item, index) => {
      console.log(`${index + 1}. L${item.lineNum}: "${item.line}"`);
      console.log(`   字节偏移量: ${item.offset}`);
    });
    
    console.log('\n=== 第二步：AST解析的IfStatement ===');
    const ast = parseSync(sourceCode, {
      syntax: 'typescript',
      tsx: true,
      comments: false,
    });
    
    const astIfStatements: any[] = [];
    
    function visitNode(node: any, path: string = 'root') {
      if (!node || typeof node !== 'object') return;
      
      if (node.type === 'IfStatement') {
        astIfStatements.push({
          span: node.span,
          path,
          test: node.test
        });
      }
      
      for (const [key, value] of Object.entries(node)) {
        if (Array.isArray(value)) {
          value.forEach((child, index) => {
            visitNode(child, `${path}.${key}[${index}]`);
          });
        } else if (value && typeof value === 'object') {
          visitNode(value, `${path}.${key}`);
        }
      }
    }
    
    visitNode(ast);
    
    console.log(`📊 AST中找到 ${astIfStatements.length} 个IfStatement节点:`);
    astIfStatements.forEach((stmt, index) => {
      console.log(`${index + 1}. Span ${stmt.span.start}-${stmt.span.end}`);
      
      // 获取span内容
      const spanContent = sourceCode.slice(stmt.span.start, Math.min(stmt.span.end, stmt.span.start + 50));
      console.log(`   内容: "${spanContent.replace(/\n/g, '\\n')}"`);
      
      if (stmt.test && stmt.test.span) {
        const testContent = sourceCode.slice(stmt.test.span.start, stmt.test.span.end);
        console.log(`   测试条件: "${testContent.replace(/\n/g, '\\n')}"`);
      }
      
      // 手动验证这个节点
      console.log(`   验证结果: ${manualValidateIfStatement(stmt, sourceCode) ? '✅ 有效' : '❌ 无效'}`);
      console.log();
    });
    
    console.log('\n=== 第三步：使用我们的分析器 ===');
    const result = await analyzeCode(sourceCode, filePath, {
      enableDetails: true,
      enableDebugLog: true
    });
    
    console.log('\n📊 我们的分析器结果:');
    let foundIfComplexity = 0;
    result.functions.forEach((fn) => {
      if (fn.details) {
        fn.details.forEach((step) => {
          if (step.ruleId === 'if-statement' || step.description.includes('条件语句')) {
            foundIfComplexity++;
            console.log(`✅ 找到if语句复杂度: L${step.line}:C${step.column} - ${step.description}`);
          }
        });
      }
    });
    
    console.log(`\n📈 总结:`);
    console.log(`- 源码中的if语句: ${realIfLines.length} 个`);
    console.log(`- AST中的IfStatement: ${astIfStatements.length} 个`);
    console.log(`- 分析器检测到的if复杂度: ${foundIfComplexity} 个`);
    
    if (realIfLines.length > foundIfComplexity) {
      console.log(`\n⚠️  有 ${realIfLines.length - foundIfComplexity} 个真实if语句未被检测到！`);
    }
    
  } catch (error) {
    console.error('调试失败:', error);
  }
}

function manualValidateIfStatement(stmt: any, sourceCode: string): boolean {
  // 检查基本结构
  if (!stmt.test) {
    console.log(`   ❌ 没有test属性`);
    return false;
  }
  
  // 检查test条件
  if (stmt.test && stmt.test.span) {
    const testStart = stmt.test.span.start; 
    const testEnd = stmt.test.span.end;
    
    if (testStart < 0 || testEnd > sourceCode.length || testStart >= testEnd) {
      console.log(`   ❌ 非法的test span ${testStart}-${testEnd}`);
      return false;
    }
    
    const testContent = sourceCode.slice(testStart, testEnd);
    
    const suspiciousPatterns = [
      /^\s*[a-zA-Z_$][a-zA-Z0-9_$]*\s*:/, // 对象属性模式 "key:"
      /^[^()]*:\s*\{/, // 对象属性后跟对象字面量
      /^\s*exportParams\s*:/, // 特定的fireExport问题模式
      /^\s*bizType\s*,/, // 特定的bizType问题模式
    ];
    
    for (const pattern of suspiciousPatterns) {
      if (pattern.test(testContent)) {
        console.log(`   ❌ 条件包含可疑内容: "${testContent}"`);
        return false;
      }
    }
  }
  
  // 检查实际源码内容
  if (stmt.span) {
    const spanStart = stmt.span.start;
    
    if (spanStart < 0 || spanStart >= sourceCode.length) {
      console.log(`   ❌ 非法的span起始位置 ${spanStart}`);
      return false;
    }
    
    // 向前查找实际的if关键字
    let foundIf = false;
    for (let i = Math.max(0, spanStart - 50); i <= Math.min(sourceCode.length - 2, spanStart + 50); i++) {
      if (sourceCode.slice(i, i + 2) === 'if' && 
          (i === 0 || /\s/.test(sourceCode[i - 1])) && 
          (i + 2 >= sourceCode.length || /\s|\(/.test(sourceCode[i + 2]))) {
        foundIf = true;
        break;
      }
    }
    
    if (!foundIf) {
      const contextStart = Math.max(0, spanStart - 20);
      const contextEnd = Math.min(sourceCode.length, spanStart + 30);
      const context = sourceCode.slice(contextStart, contextEnd);
      console.log(`   ❌ 附近未找到'if'关键字，上下文: "${context}"`);
      return false;
    }
  }
  
  return true;
}

debugIfStatementValidation();