import { parseSync } from '@swc/core';
import * as fs from 'fs';

function debugIfStatementParsing() {
  console.log('🔍 调试IfStatement解析问题...\n');
  
  try {
    const filePath = '/Users/<USER>/projj/git.imile.com/ux/ds-web/apps/ds/src/pages/InventoryEE/NDR/index.tsx';
    const sourceCode = fs.readFileSync(filePath, 'utf-8');
    
    // 解析AST
    const ast = parseSync(sourceCode, {
      syntax: 'typescript',
      tsx: true,
      comments: false,
    });
    
    // 查找所有的IfStatement节点
    const ifStatements: any[] = [];
    
    function visitNode(node: any, path: string = 'root') {
      if (!node || typeof node !== 'object') return;
      
      if (node.type === 'IfStatement') {
        ifStatements.push({
          span: node.span,
          path: path,
          test: node.test,
          consequent: node.consequent,
          alternate: node.alternate
        });
      }
      
      // 递归访问子节点
      for (const [key, value] of Object.entries(node)) {
        if (Array.isArray(value)) {
          value.forEach((child, index) => {
            visitNode(child, `${path}.${key}[${index}]`);
          });
        } else if (value && typeof value === 'object') {
          visitNode(value, `${path}.${key}`);
        }
      }
    }
    
    visitNode(ast);
    
    console.log(`📊 找到 ${ifStatements.length} 个IfStatement节点:\n`);
    
    ifStatements.forEach((stmt, index) => {
      console.log(`${index + 1}. IfStatement`);
      console.log(`   Span: ${stmt.span.start}-${stmt.span.end}`);
      console.log(`   路径: ${stmt.path}`);
      
      // 显示实际的源码内容 (只显示前100个字符)
      const actualContent = sourceCode.slice(stmt.span.start, Math.min(stmt.span.end, stmt.span.start + 100));
      console.log(`   内容: "${actualContent}${stmt.span.end - stmt.span.start > 100 ? '...' : ''}"`);
      
      // 显示条件表达式
      if (stmt.test) {
        const testContent = sourceCode.slice(stmt.test.span.start, stmt.test.span.end);
        console.log(`   条件: "${testContent}"`);
      }
      
      console.log();
    });
    
    // 检查问题节点 - span 6799
    const problemNode = ifStatements.find(stmt => stmt.span.start === 6799);
    if (problemNode) {
      console.log('🚨 发现问题节点 (span 6799):');
      console.log('这个节点被错误地识别为IfStatement，但实际应该是对象字面量');
      
      // 显示完整的测试条件
      if (problemNode.test) {
        const testStart = problemNode.test.span.start;
        const testEnd = problemNode.test.span.end;
        const testContent = sourceCode.slice(testStart, testEnd);
        console.log(`测试条件内容: "${testContent}"`);
        console.log(`测试条件span: ${testStart}-${testEnd}`);
      }
    }
    
  } catch (error) {
    console.error('调试失败:', error);
  }
}

debugIfStatementParsing();