/**
 * 调试实际文件的AST节点类型
 */

import { analyzeCode } from './src/index';
import * as fs from 'fs';

async function debugActualFile() {
  console.log('🔍 调试实际文件的AST节点类型...\n');
  
  try {
    const filePath = '/Users/<USER>/projj/git.imile.com/ux/ds-web/apps/ds/src/pages/InventoryEE/NDR/index.tsx';
    const sourceCode = fs.readFileSync(filePath, 'utf-8');
    
    console.log('📄 分析目标行内容:');
    const lines = sourceCode.split('\n');
    console.log(`L243: "${lines[242] || 'N/A'}"`);  // fireExport行
    console.log(`L245: "${lines[244] || 'N/A'}"`);  // bizType行
    console.log();
    
    const result = await analyzeCode(sourceCode, filePath, {
      enableDetails: true,
      enableDebugLog: true
    });

    console.log('📊 详细分析结果:');
    result.functions.forEach((fn) => {
      if (fn.name === 'handleExport' && fn.details) {
        console.log(`\n🔧 函数: ${fn.name}`);
        fn.details.forEach((step, index) => {
          console.log(`  步骤${index + 1}: L${step.line}:C${step.column}`);
          console.log(`    ruleId: "${step.ruleId}"`);
          console.log(`    description: "${step.description}"`);
          console.log(`    nodeType: "${step.nodeType || 'N/A'}"`);
          console.log(`    increment: ${step.increment}`);
          if (step.diagnosticMessage) {
            console.log(`    诊断: ${step.diagnosticMessage}`);
          }
          console.log();
        });
      }
    });
    
  } catch (error) {
    console.error('调试失败:', error);
  }
}

debugActualFile();