import { analyzeCode } from './src/index';

const complexCode = `
function complexFunction(x) {
  if (x > 0) {          // +1 复杂度
    while (x > 10) {    // +1 复杂度
      x--;
    }
    
    try {
      doSomething(x);
    } catch (error) {    // +1 复杂度
      console.error(error);
    }
  }
  
  return x > 5 ? 'high' : 'low';  // +1 复杂度 (三元操作符)
}
`;

async function debugComplexFunction() {
  console.log('🔍 调试复杂函数计算...\n');
  
  try {
    const result = await analyzeCode(complexCode, 'complex-test.js', {
      enableDebugLog: true, // 启用调试日志
      includeDetails: true,
      enableShowContext: true
    });
    
    console.log('\n📊 分析结果:');
    console.log(`总复杂度: ${result.complexity}`);
    console.log(`函数数量: ${result.functions.length}`);
    
    if (result.functions.length > 0) {
      result.functions.forEach(func => {
        console.log(`\n🔧 函数: ${func.name}`);
        console.log(`  复杂度: ${func.complexity}`);
        console.log(`  位置: L${func.line}:C${func.column}`);
        
        if (func.details && func.details.length > 0) {
          console.log('  详细步骤:');
          func.details.forEach((detail, index) => {
            console.log(`    ${index + 1}. L${detail.line}:C${detail.column} +${detail.increment} - ${detail.description} (规则: ${detail.ruleId}, 节点: ${detail.nodeType})`);
          });
        }
      });
    }
    
  } catch (error) {
    console.error('❌ 分析失败:', error);
  }
}

debugComplexFunction();