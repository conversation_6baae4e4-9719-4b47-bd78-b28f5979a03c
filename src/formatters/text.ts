import { BaseFormatter } from './base';
import type { AnalysisResult, FileResult, FunctionResult, DetailStep } from '../core/types';
import type { CLIOptions, CognitiveConfig } from '../config/types';
import { writeFile } from 'fs/promises';
import chalk from 'chalk';
import { getCodeFrameGenerator, type CodeFrameOptions } from '../utils/code-frame-generator';
import { createSmartContextFilter, getDefaultFilterOptions, type FilterOptions } from './smart-filter';
import { formatSpanDebugInfo } from '../utils/span-debug-info';

export class TextFormatter extends BaseFormatter {
  private codeFrameGenerator = getCodeFrameGenerator();
  private smartFilter = createSmartContextFilter();
  
  constructor(config?: CognitiveConfig) {
    // 提供默认配置以防止未定义错误
    const defaultConfig: CognitiveConfig = {
      failOnComplexity: 15,
      exclude: [],
      report: {},
      severityMapping: [
        { level: 'Critical', threshold: 25 },
        { level: 'Warning', threshold: 15 },
        { level: 'Info', threshold: 10 }
      ],
      minFileComplexity: 1,
      minFunctionComplexity: 1
    };
    
    super(config || defaultConfig);
  }
  
  public override async format(result: AnalysisResult, showDetails?: boolean, options?: CLIOptions): Promise<string> {
    const lines: string[] = [];
    
    try {
      // 应用文件复杂度过滤（使用异步版本）
      const filteredResult = await this.applyFileComplexityFilter(result, options);
      
      // 输出汇总信息
      lines.push(this.formatSummary(filteredResult.summary));
      lines.push('');
      
      // 显示过滤统计信息（非静默模式）
      const filterSummary = this.getFilterSummary(filteredResult, options);
      if (filterSummary) {
        lines.push(chalk.gray('📊 ' + filterSummary));
        lines.push('');
      }
      
      // 如果没有详细模式，只显示高复杂度函数
      // 但是在debug模式下，即使复杂度不高也要显示函数以便查看span信息
      if (!showDetails && filteredResult.summary.highComplexityFunctions === 0 && !options?.debug) {
        lines.push(chalk.green('✅ 所有函数复杂度都在阈值范围内'));
        return lines.join('\n');
      }
      
      // 输出详细结果
      if (filteredResult.results.length > 0) {
        let title;
        if (showDetails) {
          title = '📋 详细分析结果:';
        } else if (options?.debug) {
          title = '🔍 调试模式 - 函数分析结果:';
        } else {
          title = '⚠️  需要关注的高复杂度函数:';
        }
        lines.push(chalk.cyan(title));
        lines.push(chalk.gray('─'.repeat(50)));
        lines.push('');
        
        for (const fileResult of filteredResult.results) {
          const formattedFile = await this.formatFileResult(fileResult, showDetails, options);
          if (formattedFile.trim()) {
            lines.push(formattedFile);
          }
        }
      } else {
        // 检查是否所有文件都被过滤了
        const filterStats = this.getFilterStatistics(filteredResult);
        if (filterStats && filterStats.totalFiles > 0 && filterStats.displayedFiles === 0) {
          lines.push(chalk.yellow(`⚠️  所有 ${filterStats.totalFiles} 个文件都被过滤（复杂度 < ${filterStats.threshold}）`));
        } else {
          lines.push(chalk.yellow('⚠️  未发现需要分析的文件'));
        }
      }
      
      return lines.join('\n');
      
    } catch (error) {
      // 格式化错误时的优雅降级
      console.error('文本格式化失败，使用同步版本:', error);
      
      try {
        // 回退到同步版本
        const fallbackResult = this.applyFileComplexityFilterSync(result, options);
        
        // 简化的错误恢复格式化
        const fallbackLines: string[] = [];
        fallbackLines.push(chalk.red('⚠️  格式化过程中遇到问题，使用简化输出'));
        fallbackLines.push('');
        fallbackLines.push(this.formatSummary(fallbackResult.summary));
        
        if (fallbackResult.results.length > 0) {
          fallbackLines.push('');
          fallbackLines.push(chalk.cyan('📋 文件列表:'));
          for (const fileResult of fallbackResult.results) {
            fallbackLines.push(`  • ${fileResult.filePath || '<unknown>'} (复杂度: ${fileResult.complexity})`);
          }
        }
        
        return fallbackLines.join('\n');
        
      } catch (fallbackError) {
        // 最后的回退：基本错误信息
        return [
          chalk.red('❌ 文本格式化失败'),
          chalk.gray(`原始错误: ${error instanceof Error ? error.message : String(error)}`),
          chalk.gray(`回退错误: ${fallbackError instanceof Error ? fallbackError.message : String(fallbackError)}`),
          '',
          chalk.yellow('💡 建议: 请检查输入数据或尝试使用其他格式')
        ].join('\n');
      }
    }
  }

  public override async writeToFile(result: AnalysisResult, outputPath: string, options?: CLIOptions): Promise<void> {
    try {
      // 应用文件复杂度过滤（使用异步版本）
      const filteredResult = await this.applyFileComplexityFilter(result, options);
      const content = await this.format(filteredResult, true, options); // 文件输出时总是显示详细信息
      
      await writeFile(outputPath, content, 'utf-8');
    } catch (error) {
      throw new Error(`无法写入文本文件 ${outputPath}: ${error instanceof Error ? error.message : String(error)}`);
    }
  }
  
  private formatSummary(summary: AnalysisResult['summary']): string {
    const lines: string[] = [];
    
    lines.push(chalk.bold.cyan('🧠 认知复杂度分析汇总'));
    lines.push(chalk.gray('='.repeat(25)));
    lines.push(chalk.cyan('📁 分析文件数: ') + chalk.white(summary.filesAnalyzed.toString()));
    lines.push(chalk.cyan('🔍 分析函数数: ') + chalk.white(summary.functionsAnalyzed.toString()));
    lines.push(chalk.cyan('📊 平均复杂度: ') + this.formatComplexityWithColors(summary.averageComplexity));
    lines.push(chalk.cyan('📈 总复杂度: ') + chalk.white(summary.totalComplexity.toString()));
    
    if (summary.highComplexityFunctions > 0) {
      lines.push(chalk.cyan('⚠️  高复杂度函数: ') + chalk.yellow(summary.highComplexityFunctions.toString()));
    } else {
      lines.push(chalk.cyan('✅ 高复杂度函数: ') + chalk.green('0'));
    }
    
    return lines.join('\n');
  }
  
  private async formatFileResult(fileResult: FileResult, showDetails?: boolean, options?: CLIOptions): Promise<string> {
    const lines: string[] = [];
    
    // 文件头信息
    const complexityStr = this.formatComplexityWithColors(fileResult.complexity);
    const avgStr = this.formatComplexityWithColors(fileResult.averageComplexity);
    lines.push(chalk.blue('📄 ') + chalk.bold(fileResult.filePath) + 
               chalk.gray(` (复杂度: ${complexityStr}, 平均: ${avgStr})`));
    
    // 函数详情
    if (fileResult.functions.length > 0) {
      const sortedFunctions = this.sortFunctions(fileResult.functions);
      
      // 应用函数级别的复杂度过滤
      const filteredFunctions = this.applyFunctionComplexityFilter(sortedFunctions, showDetails, options);
      
      if (filteredFunctions.length > 0) {
        for (const func of filteredFunctions) {
          const formattedFunc = await this.formatFunctionResult(func, showDetails, options, fileResult.filePath);
          lines.push(formattedFunc);
        }
      } else {
        // 所有函数都被过滤了
        const totalFunctions = sortedFunctions.length;
        const hiddenFunctions = totalFunctions - filteredFunctions.length;
        const threshold = this.getFunctionComplexityThreshold(options);
        
        if (hiddenFunctions > 0 && !options?.quiet) {
          lines.push(chalk.gray(`  ℹ️  ${hiddenFunctions}个函数复杂度低于阈值${threshold}，已隐藏`));
        } else {
          lines.push(chalk.gray('  ℹ️  未发现函数'));
        }
      }
    } else {
      lines.push(chalk.gray('  ℹ️  未发现函数'));
    }
    
    lines.push(''); // 文件间空行
    return lines.join('\n');
  }
  
  private async formatFunctionResult(func: FunctionResult, showDetails?: boolean, options?: CLIOptions, filePath?: string): Promise<string> {
    const lines: string[] = [];
    const complexityStr = this.formatComplexityWithColors(func.complexity);
    const position = chalk.gray(`${func.line}:${func.column}`);
    
    let icon = '  🔧';
    const severity = this.getSeverityLevel(func.complexity);
    
    switch (severity) {
      case 'Critical':
        icon = '  🚨';
        break;
      case 'Warning':
        icon = '  ⚠️ ';
        break;
      case 'Info':
        icon = '  ℹ️ ';
        break;
    }
    
    let result = `${icon} ${chalk.bold(func.name)} (${position}) - 最终复杂度: ${complexityStr}`;
    
    // 添加豁免信息
    if (func.ignoreExemptions && func.ignoreExemptions.length > 0) {
      const totalExempted = func.ignoreExemptions.reduce((sum, ex) => sum + ex.complexityReduced, 0);
      result += chalk.gray(` [已豁免: ${totalExempted}分, ${func.ignoreExemptions.length}处]`);
    }
    
    lines.push(result);
    
    // 在 debug 模式下显示函数级别的 SWC span 信息（即使没有详细模式）
    if (options?.debug && func.details && func.details.length > 0 && filePath) {
      // 查找函数定义的步骤（通常是第一个或具有函数相关nodeType的步骤）
      const functionStep = func.details.find(step => 
        step.nodeType && (
          step.nodeType.includes('Function') || 
          step.nodeType.includes('Arrow') || 
          step.nodeType.includes('Method')
        )
      ) || func.details[0]; // 如果找不到函数步骤，使用第一个步骤
      
      if (functionStep) {
        const debugInfoText = await formatSpanDebugInfo(functionStep, filePath, options);
        if (debugInfoText) {
          lines.push(`      ${debugInfoText}`);
        }
      }
    }
    
    // 如果启用详细模式且有详细步骤，显示详细计算过程
    if (showDetails && func.details && func.details.length > 0) {
      const detailsText = await this.formatDetailSteps(func.details, options, filePath);
      lines.push(detailsText);
    }
    
    return lines.join('\n');
  }
  
  private sortFunctions(functions: FunctionResult[]): FunctionResult[] {
    // 默认按复杂度从高到低排序
    return [...functions].sort((a, b) => b.complexity - a.complexity);
  }

  /**
   * 应用函数级别的复杂度过滤
   * @param functions 函数列表
   * @param showDetails 是否显示详细信息
   * @param options CLI选项
   * @returns 过滤后的函数列表
   */
  private applyFunctionComplexityFilter(functions: FunctionResult[], showDetails?: boolean, options?: CLIOptions): FunctionResult[] {
    const threshold = this.getFunctionComplexityThreshold(options);
    
    // 如果阈值为0或负数，显示所有函数
    if (threshold <= 0) {
      return functions;
    }
    
    // 在详细模式下，可以通过参数控制是否显示零复杂度函数
    if (showDetails && options?.showZeroComplexity) {
      return functions;
    }
    
    // 过滤掉复杂度低于阈值的函数
    return functions.filter(func => func.complexity >= threshold);
  }

  /**
   * 获取函数复杂度过滤阈值
   * @param options CLI选项
   * @returns 复杂度阈值
   */
  private getFunctionComplexityThreshold(options?: CLIOptions): number {
    // 优先级：CLI参数 > 配置文件 > 默认值1
    return options?.minFunctionComplexity ?? this.config?.minFunctionComplexity ?? 1;
  }

  /**
   * 格式化详细计算步骤
   * 按照技术规格书格式：L<行号>: +<分数> (累计: <累计分>) - <规则描述> [<规则ID>] (嵌套层级: <层级>)
   */
  private async formatDetailSteps(details: DetailStep[], options?: CLIOptions, filePath?: string): Promise<string> {
    const lines: string[] = [];
    
    // 创建过滤选项
    const filterOptions = this.createFilterOptions(options);
    
    // 应用智能过滤
    const filteredSteps = this.smartFilter.filterDetailSteps(details, filterOptions);
    
    // 显示过滤统计信息（如果有过滤发生）
    if (filteredSteps.length < details.length && !options?.quiet) {
      const summary = this.smartFilter.getFilterSummary(details.length, filteredSteps.length);
      lines.push(chalk.gray(`      📊 显示 ${filteredSteps.length}/${details.length} 个步骤 (${summary.filterReason})`));
      lines.push('');
    }
    
    for (let i = 0; i < filteredSteps.length; i++) {
      const step = filteredSteps[i];
      if (!step) continue; // 跳过undefined步骤
      
      const line = this.formatDetailStep(step);
      lines.push(line);
      
      // 如果启用了代码上下文显示，生成代码框架
      if (options?.showContext && this.shouldShowContext(step, i, filterOptions, options)) {
        const contextFrame = await this.generateContextFrame(step, filePath, options);
        if (contextFrame) {
          lines.push(contextFrame);
        }
      }
    }
    
    return lines.join('\n');
  }

  /**
   * 格式化单个详细步骤
   * 实现符合技术规格书的输出格式
   */
  private formatDetailStep(step: DetailStep): string {
    const baseIndent = '      '; // 基础缩进
    const additionalIndent = '  '.repeat(Math.max(0, step.nestingLevel)); // 嵌套层级缩进
    const fullIndent = baseIndent + additionalIndent;
    
    // 格式化行号，确保对齐
    const lineNumber = `L${step.line}`;
    
    // 格式化增量，支持负数（豁免）
    const incrementStr = step.increment >= 0 ? `+${step.increment}` : `${step.increment}`;
    const incrementColored = step.increment >= 0 
      ? chalk.green(incrementStr) 
      : chalk.red(incrementStr);
    
    // 格式化累计复杂度
    const cumulativeStr = chalk.cyan(`累计: ${step.cumulative}`);
    
    // 格式化规则描述，添加诊断标记
    const ruleDescription = this.formatRuleDescription(step);
    
    // 格式化规则ID
    const ruleId = chalk.gray(`[${step.ruleId}]`);
    
    // 格式化嵌套层级
    const nestingLevel = chalk.magenta(`(嵌套层级: ${step.nestingLevel})`);
    
    return `${fullIndent}- ${chalk.yellow(lineNumber)}: ${incrementColored} (${cumulativeStr}) - ${ruleDescription} ${ruleId} ${nestingLevel}`;
  }

  /**
   * 判断是否应该显示代码上下文
   */
  private shouldShowContext(step: DetailStep, rank: number, filterOptions: FilterOptions, options?: CLIOptions): boolean {
    // 如果显式设置了shouldShowContext，使用该设置
    if (step.shouldShowContext !== undefined) {
      return step.shouldShowContext;
    }
    
    // 如果启用了显示所有上下文，总是显示
    if (options?.showAllContext) {
      return true;
    }
    
    // 使用智能过滤器判断
    return this.smartFilter.shouldShowContext(step, rank, filterOptions);
  }
  
  /**
   * 创建过滤选项
   */
  private createFilterOptions(options?: CLIOptions): FilterOptions {
    const defaultOptions = getDefaultFilterOptions();
    
    return {
      minComplexityIncrement: options?.minComplexityIncrement ?? defaultOptions.minComplexityIncrement,
      maxContextItems: options?.maxContextItems ?? defaultOptions.maxContextItems,
      forceShowAll: options?.showAllContext ?? defaultOptions.forceShowAll
    };
  }
  
  /**
   * 生成代码上下文框架（集成错误恢复机制）
   */
  private async generateContextFrame(step: DetailStep, filePath?: string, options?: CLIOptions): Promise<string | null> {
    if (!filePath) {
      return this.generateFallbackContextInfo(step, '文件路径未提供');
    }
    
    try {
      const contextLines = options?.contextLines ?? 2;
      const frameOptions: CodeFrameOptions = {
        highlightCode: true,
        linesAbove: contextLines,
        linesBelow: contextLines,
        forceColor: false
      };
      
      // 不在上下文框架中重复显示调试信息，因为函数级别已经显示过了
      // 只在详细步骤中需要时才显示步骤级别的调试信息
      let debugInfo = '';
      // 注释掉重复的调试信息显示
      // if (options?.debug) {
      //   const debugInfoText = await formatSpanDebugInfo(step, filePath, options);
      //   if (debugInfoText) {
      //     debugInfo = `        ${debugInfoText}\n\n`;
      //   }
      // }
      
      let frameResult;
      if (step.span) {
        // 使用span信息生成更精确的代码框架
        frameResult = await this.codeFrameGenerator.generateFrameFromSpan(
          filePath,
          step.span,
          frameOptions,
          step.nodeType
        );
      } else {
        // 使用行列信息生成代码框架
        frameResult = await this.codeFrameGenerator.generateFrame(
          filePath,
          step.line,
          step.column,
          frameOptions
        );
      }
      
      if (frameResult.success) {
        // 给代码框架添加缩进和边框
        const indentedFrame = frameResult.frame
          .split('\n')
          .map(line => `        ${chalk.gray('│')} ${line}`)
          .join('\n');
        
        // 添加错误恢复状态信息（如果有恢复尝试）
        let recoveryInfo = '';
        if (frameResult.recoveryAttempts && frameResult.recoveryAttempts > 1) {
          recoveryInfo = `\n${chalk.gray(`        📊 错误恢复: ${frameResult.recoveryAttempts}次尝试, 策略: ${frameResult.recoveryStrategy}`)}`;
        }
        
        return `${debugInfo}\n${chalk.gray('        ┌─ 代码上下文 ─────')}\n${indentedFrame}\n${chalk.gray('        └─────────────────')}${recoveryInfo}`;
      } else {
        // 使用错误恢复机制生成降级上下文
        return this.generateRecoveryContextFrame(step, filePath, frameResult);
      }
    } catch (error) {
      // 最终的错误恢复：生成基础上下文信息
      return this.generateFallbackContextInfo(step, error instanceof Error ? error.message : String(error));
    }
  }

  /**
   * 生成错误恢复的上下文框架
   */
  private generateRecoveryContextFrame(step: DetailStep, filePath: string, frameResult: any): string {
    const lines: string[] = [];
    
    // 显示错误恢复尝试的详细信息
    lines.push(chalk.gray('        ┌─ 代码上下文（错误恢复模式）─────'));
    
    // 基础文件和位置信息
    const relativePath = filePath.replace(process.cwd() + '/', '');
    lines.push(`        ${chalk.gray('│')} ${chalk.cyan('📄 文件:')} ${chalk.white(relativePath)}`);
    lines.push(`        ${chalk.gray('│')} ${chalk.cyan('📍 位置:')} ${chalk.white(`第 ${step.line} 行，第 ${step.column} 列`)}`);
    lines.push(`        ${chalk.gray('│')} ${chalk.cyan('🔍 规则:')} ${chalk.white(step.ruleId)}`);
    lines.push(`        ${chalk.gray('│')} ${chalk.cyan('📈 复杂度增量:')} ${step.increment >= 0 ? chalk.green(`+${step.increment}`) : chalk.red(`${step.increment}`)}`);
    
    // 错误恢复状态信息
    if (frameResult.recoveryAttempts) {
      lines.push(`        ${chalk.gray('│')} ${chalk.yellow('🔄 恢复尝试:')} ${chalk.white(frameResult.recoveryAttempts + '次')}`);
    }
    if (frameResult.recoveryStrategy) {
      lines.push(`        ${chalk.gray('│')} ${chalk.yellow('📋 恢复策略:')} ${chalk.white(frameResult.recoveryStrategy)}`);
    }
    
    // 错误信息
    if (frameResult.error) {
      lines.push(`        ${chalk.gray('│')} ${chalk.red('⚠️  错误:')} ${chalk.gray(frameResult.error)}`);
    }
    
    // 使用span信息的情况下，尝试提供更多上下文
    if (step.span) {
      lines.push(`        ${chalk.gray('│')} ${chalk.cyan('📏 代码范围:')} ${chalk.white(`${step.span.start}-${step.span.end}`)}`);
    }
    
    // 如果有上下文描述，显示它
    if (step.context && step.context.trim()) {
      lines.push(`        ${chalk.gray('│')} ${chalk.cyan('💬 描述:')} ${chalk.white(step.context.trim())}`);
    }
    
    lines.push(chalk.gray('        └─────────────────────────────────'));
    
    return '\n' + lines.join('\n');
  }

  /**
   * 生成最基础的降级上下文信息
   */
  private generateFallbackContextInfo(step: DetailStep, errorReason: string): string {
    const lines: string[] = [];
    
    lines.push(chalk.gray('        ┌─ 基础上下文信息 ─────'));
    lines.push(`        ${chalk.gray('│')} ${chalk.cyan('📍 位置:')} ${chalk.white(`第 ${step.line} 行，第 ${step.column} 列`)}`);
    lines.push(`        ${chalk.gray('│')} ${chalk.cyan('🔍 规则:')} ${chalk.white(step.ruleId)}`);
    lines.push(`        ${chalk.gray('│')} ${chalk.cyan('📈 复杂度:')} ${step.increment >= 0 ? chalk.green(`+${step.increment}`) : chalk.red(`${step.increment}`)} ${chalk.gray(`(累计: ${step.cumulative})`)}`);
    lines.push(`        ${chalk.gray('│')} ${chalk.cyan('🏗️  嵌套层级:')} ${chalk.white(step.nestingLevel.toString())}`);
    
    if (step.context && step.context.trim()) {
      lines.push(`        ${chalk.gray('│')} ${chalk.cyan('💬 描述:')} ${chalk.white(step.context.trim())}`);
    }
    
    lines.push(`        ${chalk.gray('│')} ${chalk.red('⚠️  上下文生成失败:')} ${chalk.gray(errorReason)}`);
    lines.push(chalk.gray('        └─────────────────────────'));
    
    return '\n' + lines.join('\n');
  }
  
  /**
   * 格式化规则描述，添加诊断标记
   */
  private formatRuleDescription(step: DetailStep): string {
    let description = step.description;
    
    // 添加诊断标记
    if (step.ruleId === 'unknown-rule' || step.description.includes('未知')) {
      description = `❓ ${description}`;
    } else if (step.increment < 0) {
      // 负增量（如豁免）使用警告标记
      description = `⚠️ ${description}`;
    } else if (step.diagnosticMarker) {
      // 根据诊断标记类型添加相应emoji
      switch (step.diagnosticMarker) {
        case 'warning':
          description = `⚠️ ${description}`;
          break;
        case 'unknown':
          description = `❓ ${description}`;
          break;
        case 'error':
          description = `❌ ${description}`;
          break;
      }
    }
    
    return chalk.white(description);
  }

  private formatComplexityWithColors(complexity: number, threshold: number = 15): string {
    if (complexity > threshold * 1.5) {
      return chalk.red.bold(complexity.toFixed(2));
    } else if (complexity > threshold) {
      return chalk.yellow(complexity.toFixed(2));
    } else if (complexity > threshold * 0.7) {
      return chalk.blue(complexity.toFixed(2));
    } else {
      return chalk.green(complexity.toFixed(2));
    }
  }
}