/**
 * 🔧 SWC解析器补偿机制：扫描未被AST正确解析的if语句
 * 当SWC解析器失败时，使用源码级别的扫描来找到真正的if语句
 */

import * as fs from 'fs';

export interface IfStatementMatch {
  line: number;
  column: number;
  offset: number;
  content: string;
  condition: string;
}

export class SWCParserCompensation {
  private sourceCode: string;
  private lines: string[];

  constructor(sourceCode: string) {
    this.sourceCode = sourceCode;
    this.lines = sourceCode.split('\n');
  }

  /**
   * 扫描源码中的真正if语句
   * @returns if语句匹配结果
   */
  public scanForIfStatements(): IfStatementMatch[] {
    const matches: IfStatementMatch[] = [];
    let currentOffset = 0;

    for (let lineIndex = 0; lineIndex < this.lines.length; lineIndex++) {
      const line = this.lines[lineIndex];
      const trimmedLine = line.trim();
      
      // 查找if关键字的位置
      let searchStart = 0;
      while (true) {
        const ifIndex = line.indexOf('if', searchStart);
        if (ifIndex === -1) break;
        
        // 验证这是一个真正的if语句关键字
        if (this.isValidIfKeyword(line, ifIndex)) {
          // 提取条件
          const condition = this.extractCondition(line, ifIndex);
          if (condition) {
            matches.push({
              line: lineIndex + 1, // 1-based
              column: ifIndex + 1, // 1-based
              offset: currentOffset + ifIndex,
              content: trimmedLine,
              condition: condition
            });
          }
        }
        
        searchStart = ifIndex + 1;
      }
      
      currentOffset += line.length + 1; // +1 for newline
    }

    return matches;
  }

  /**
   * 验证是否是有效的if关键字
   * @param line 代码行
   * @param ifIndex if关键字的位置
   * @returns 是否有效
   */
  private isValidIfKeyword(line: string, ifIndex: number): boolean {
    // 检查前面的字符
    if (ifIndex > 0) {
      const prevChar = line[ifIndex - 1];
      // 前面必须是空白字符、开括号、分号等分隔符
      if (!/[\s\{\;\(\)]/.test(prevChar)) {
        return false;
      }
    }

    // 检查后面的字符
    if (ifIndex + 2 < line.length) {
      const nextChar = line[ifIndex + 2];
      // 后面必须是空白字符或开括号
      if (!/[\s\(]/.test(nextChar)) {
        return false;
      }
    }

    // 排除注释中的if
    const beforeIf = line.substring(0, ifIndex);
    if (beforeIf.includes('//')) {
      return false;
    }

    // 排除字符串中的if
    const singleQuoteCount = (beforeIf.match(/'/g) || []).length;
    const doubleQuoteCount = (beforeIf.match(/"/g) || []).length;
    const templateQuoteCount = (beforeIf.match(/`/g) || []).length;
    
    if (singleQuoteCount % 2 === 1 || doubleQuoteCount % 2 === 1 || templateQuoteCount % 2 === 1) {
      return false;
    }

    return true;
  }

  /**
   * 提取if语句的条件
   * @param line 代码行
   * @param ifIndex if关键字的位置
   * @returns 条件字符串
   */
  private extractCondition(line: string, ifIndex: number): string | null {
    // 查找条件的开始位置（第一个左括号）
    let startIndex = ifIndex + 2; // 跳过"if"
    while (startIndex < line.length && line[startIndex] !== '(') {
      if (!/\s/.test(line[startIndex])) {
        // 如果遇到非空白字符且不是左括号，说明不是标准的if语句
        return null;
      }
      startIndex++;
    }

    if (startIndex >= line.length || line[startIndex] !== '(') {
      return null;
    }

    // 查找匹配的右括号
    let parenCount = 1;
    let endIndex = startIndex + 1;
    
    while (endIndex < line.length && parenCount > 0) {
      if (line[endIndex] === '(') {
        parenCount++;
      } else if (line[endIndex] === ')') {
        parenCount--;
      }
      endIndex++;
    }

    if (parenCount === 0) {
      // 找到完整的条件
      return line.substring(startIndex + 1, endIndex - 1).trim();
    }

    // 如果在当前行没找到完整条件，可能条件跨行，这里简化处理
    return line.substring(startIndex + 1).trim();
  }

  /**
   * 将源码级if语句转换为模拟的AST节点
   * @param match if语句匹配
   * @returns 模拟的AST节点
   */
  public createSimulatedIfNode(match: IfStatementMatch): any {
    return {
      type: 'IfStatement',
      span: {
        start: match.offset,
        end: match.offset + match.content.length
      },
      test: {
        type: 'SimulatedCondition',
        span: {
          start: match.offset + match.content.indexOf(match.condition),
          end: match.offset + match.content.indexOf(match.condition) + match.condition.length
        },
        value: match.condition
      },
      // 标记这是补偿机制生成的节点
      __compensated: true,
      __originalMatch: match
    };
  }
}