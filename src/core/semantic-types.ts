import type { Node } from '@swc/core';

/**
 * 语义上下文 - 描述代码节点的语义信息
 */
export interface SemanticContext {
  /** 节点类型 */
  nodeType: string;
  /** 父级语义上下文 */
  parentContext?: SemanticContext;
  /** 代码模式分类 */
  codePattern: 'jsx' | 'function' | 'control-flow' | 'expression' | 'declaration';
  /** 逻辑重要性 */
  logicalImportance: 'primary' | 'secondary' | 'structural';
  /** 周围元素信息 */
  surroundingElements: {
    hasJSX: boolean;
    hasTypeScript: boolean;
    hasArrowFunctions: boolean;
    nestingLevel: number;
  };
}

/**
 * 行语义信息 - 描述代码行的语义特征
 */
export interface LineSemanticInfo {
  /** 是否逻辑上有意义 */
  isLogicallySignificant: boolean;
  /** 内容类型 */
  contentType: LineContentType;
  /** 逻辑元素列表 */
  logicalElements: LogicalElement[];
  /** 语法提示 */
  syntaxHints: SyntaxHint[];
}

/**
 * 逻辑元素 - 代码行中的有意义元素
 */
export interface LogicalElement {
  /** 元素类型 */
  type: 'keyword' | 'identifier' | 'operator' | 'literal';
  /** 元素值 */
  value: string;
  /** 重要性级别 */
  importance: 'critical' | 'important' | 'minor';
  /** 位置信息 */
  position: { start: number; end: number };
}

/**
 * 语法提示 - 帮助理解代码语义的提示信息
 */
export interface SyntaxHint {
  /** 提示类型 */
  type: 'jsx-pattern' | 'arrow-function' | 'type-annotation' | 'destructuring';
  /** 置信度 */
  confidence: number;
  /** 额外信息 */
  metadata?: Record<string, any>;
}

/**
 * 位置定位策略配置
 */
export interface PositionStrategy {
  /** 策略优先级 */
  priority: 'high' | 'medium' | 'low';
  /** 主要策略名称 */
  strategy: string;
  /** 回退策略 */
  fallback: string;
  /** 语义类型 */
  semantics: string;
}

/**
 * 回退结果
 */
export interface FallbackResult {
  /** 找到的位置 */
  position: number | null;
  /** 使用的策略 */
  strategy: 'semantic' | 'structural' | 'contextual' | 'emergency';
  /** 置信度 */
  confidence: number;
  /** 回退原因 */
  reason?: string;
}

/**
 * JSX 语义信息
 */
export interface JSXSemanticInfo {
  /** JSX 元素类型 */
  elementType: 'opening-tag' | 'closing-tag' | 'self-closing' | 'expression';
  /** 是否包含逻辑内容 */
  hasLogicalContent: boolean;
  /** 表达式位置 */
  expressionPosition?: number;
  /** 逻辑复杂度贡献 */
  logicalComplexity: number;
}

/**
 * 函数语义信息
 */
export interface FunctionSemanticInfo {
  /** 函数类型 */
  functionType: FunctionType;
  /** 参数列表位置 */
  parametersPosition?: number;
  /** 函数体位置 */
  bodyPosition?: number;
  /** 逻辑起始位置 */
  logicalStartPosition?: number;
  /** 是否为异步函数 */
  isAsync: boolean;
  /** 是否为生成器函数 */
  isGenerator: boolean;
}

/**
 * 函数类型枚举
 */
export type FunctionType = 
  | 'arrow-function'
  | 'function-expression'
  | 'function-declaration'
  | 'method-definition'
  | 'constructor';

/**
 * 行内容类型
 */
export type LineContentType = 
  | 'pure-code'
  | 'code-with-comment'
  | 'pure-comment'
  | 'whitespace-only'
  | 'structural-only'
  | 'mixed-content';

/**
 * 紧急上下文 - 当其他定位策略失败时使用
 */
export interface EmergencyContext {
  /** 节点信息 */
  node: Node;
  /** 源代码 */
  sourceCode: string;
  /** 已尝试的策略 */
  attemptedStrategies: string[];
  /** 错误信息 */
  error?: string;
}

/**
 * 语义定位选项
 */
export interface SemanticPositioningOptions {
  /** 启用 JSX 语义感知 */
  enableJSXSemantics?: boolean;
  /** 启用函数语义感知 */
  enableFunctionSemantics?: boolean;
  /** 启用智能行筛选 */
  enableSmartLineFiltering?: boolean;
  /** 最大回退层数 */
  maxFallbackLevels?: number;
  /** 调试模式 */
  debugMode?: boolean;
}

/**
 * 语义策略映射表
 */
export const SEMANTIC_POSITION_STRATEGIES: Record<string, PositionStrategy> = {
  // JSX 相关策略
  'JSXElement': {
    priority: 'high',
    strategy: 'find-opening-tag-start',
    fallback: 'find-jsx-content-start',
    semantics: 'jsx-structure'
  },
  'JSXExpressionContainer': {
    priority: 'high',
    strategy: 'find-expression-content',
    fallback: 'find-brace-content',
    semantics: 'jsx-logic'
  },
  'JSXFragment': {
    priority: 'medium',
    strategy: 'find-fragment-start',
    fallback: 'find-opening-bracket',
    semantics: 'jsx-structure'
  },

  // 函数相关策略
  'ArrowFunctionExpression': {
    priority: 'high',
    strategy: 'find-arrow-or-params',
    fallback: 'find-function-start',
    semantics: 'function-declaration'
  },
  'FunctionExpression': {
    priority: 'medium',
    strategy: 'find-function-keyword',
    fallback: 'find-params-start',
    semantics: 'function-declaration'
  },
  'FunctionDeclaration': {
    priority: 'medium',
    strategy: 'find-function-keyword',
    fallback: 'find-identifier-start',
    semantics: 'function-declaration'
  },

  // 控制流策略
  'ConditionalExpression': {
    priority: 'high',
    strategy: 'find-condition-start',
    fallback: 'find-question-mark',
    semantics: 'conditional-logic'
  },
  'IfStatement': {
    priority: 'medium',
    strategy: 'find-if-keyword',
    fallback: 'find-condition-start',
    semantics: 'control-flow'
  },
  'WhileStatement': {
    priority: 'medium',
    strategy: 'find-while-keyword',
    fallback: 'find-condition-start',
    semantics: 'control-flow'
  },
  'ForStatement': {
    priority: 'medium',
    strategy: 'find-for-keyword',
    fallback: 'find-init-statement',
    semantics: 'control-flow'
  },

  // 逻辑操作符策略
  'LogicalExpression': {
    priority: 'high',
    strategy: 'find-logical-operator',
    fallback: 'find-left-operand',
    semantics: 'logical-operation'
  },
  'BinaryExpression': {
    priority: 'medium',
    strategy: 'find-operator',
    fallback: 'find-left-operand',
    semantics: 'binary-operation'
  },

  // 新增：对象和数组相关策略
  'ObjectExpression': {
    priority: 'low',
    strategy: 'find-opening-brace',
    fallback: 'find-object-start',
    semantics: 'data-structure'
  },
  'ArrayExpression': {
    priority: 'low',
    strategy: 'find-opening-bracket',
    fallback: 'find-array-start', 
    semantics: 'data-structure'
  },
  'Property': {
    priority: 'low',
    strategy: 'find-property-key',
    fallback: 'find-property-start',
    semantics: 'data-structure'
  },

  // 新增：标识符和变量相关策略
  'Identifier': {
    priority: 'low',
    strategy: 'find-identifier-name',
    fallback: 'find-identifier-start',
    semantics: 'reference'
  },
  'VariableDeclarator': {
    priority: 'low',
    strategy: 'find-variable-name',
    fallback: 'find-variable-start',
    semantics: 'declaration'
  },
  'VariableDeclaration': {
    priority: 'low',
    strategy: 'find-var-keyword',
    fallback: 'find-declaration-start',
    semantics: 'declaration'
  },

  // 新增：成员访问和函数调用策略
  'MemberExpression': {
    priority: 'medium',
    strategy: 'find-member-dot',
    fallback: 'find-object-start',
    semantics: 'member-access'
  },
  'CallExpression': {
    priority: 'medium',
    strategy: 'find-function-name',
    fallback: 'find-call-start',
    semantics: 'function-call'
  },
  'NewExpression': {
    priority: 'medium',
    strategy: 'find-new-keyword',
    fallback: 'find-constructor-start',
    semantics: 'object-creation'
  },

  // 新增：赋值和更新表达式策略
  'AssignmentExpression': {
    priority: 'medium',
    strategy: 'find-assignment-operator',
    fallback: 'find-left-operand',
    semantics: 'assignment'
  },
  'UpdateExpression': {
    priority: 'medium',
    strategy: 'find-update-operator',
    fallback: 'find-operand',
    semantics: 'update-operation'
  },
  'UnaryExpression': {
    priority: 'medium',
    strategy: 'find-unary-operator',
    fallback: 'find-operand',
    semantics: 'unary-operation'
  },

  // 新增：语句相关策略
  'BlockStatement': {
    priority: 'low',
    strategy: 'find-opening-brace',
    fallback: 'find-block-start',
    semantics: 'block-structure'
  },
  'ExpressionStatement': {
    priority: 'low',
    strategy: 'find-expression-start',
    fallback: 'find-statement-start',
    semantics: 'statement'
  },
  'ReturnStatement': {
    priority: 'medium',
    strategy: 'find-return-keyword',
    fallback: 'find-return-value',
    semantics: 'control-flow'
  },

  // 新增：导入导出策略
  'ImportDeclaration': {
    priority: 'low',
    strategy: 'find-import-keyword',
    fallback: 'find-import-start',
    semantics: 'module-import'
  },
  'ExportDeclaration': {
    priority: 'low',
    strategy: 'find-export-keyword',
    fallback: 'find-export-start',
    semantics: 'module-export'
  },
  'ExportDefaultDeclaration': {
    priority: 'low',
    strategy: 'find-export-default',
    fallback: 'find-export-start',
    semantics: 'module-export'
  },
  'ExportNamedDeclaration': {
    priority: 'low',
    strategy: 'find-export-keyword',
    fallback: 'find-export-start',
    semantics: 'module-export'
  },

  // 新增：异常处理策略
  'TryStatement': {
    priority: 'medium',
    strategy: 'find-try-keyword',
    fallback: 'find-try-block',
    semantics: 'control-flow'
  },
  'CatchClause': {
    priority: 'medium',
    strategy: 'find-catch-keyword',
    fallback: 'find-catch-param',
    semantics: 'control-flow'
  },
  'ThrowStatement': {
    priority: 'medium',
    strategy: 'find-throw-keyword',
    fallback: 'find-throw-expression',
    semantics: 'control-flow'
  },

  // 新增：循环语句策略
  'DoWhileStatement': {
    priority: 'medium',
    strategy: 'find-do-keyword',
    fallback: 'find-loop-body',
    semantics: 'control-flow'
  },
  'ForInStatement': {
    priority: 'medium',
    strategy: 'find-for-keyword',
    fallback: 'find-in-keyword',
    semantics: 'control-flow'
  },
  'ForOfStatement': {
    priority: 'medium',
    strategy: 'find-for-keyword',
    fallback: 'find-of-keyword',
    semantics: 'control-flow'
  },

  // 新增：Switch 语句策略
  'SwitchStatement': {
    priority: 'medium',
    strategy: 'find-switch-keyword',
    fallback: 'find-switch-expression',
    semantics: 'control-flow'
  },
  'SwitchCase': {
    priority: 'medium',
    strategy: 'find-case-keyword',
    fallback: 'find-case-value',
    semantics: 'control-flow'
  },

  // 新增：类相关策略
  'ClassDeclaration': {
    priority: 'medium',
    strategy: 'find-class-keyword',
    fallback: 'find-class-name',
    semantics: 'class-declaration'
  },
  'ClassExpression': {
    priority: 'medium',
    strategy: 'find-class-keyword',
    fallback: 'find-class-start',
    semantics: 'class-declaration'
  },
  'ClassMethod': {
    priority: 'medium',
    strategy: 'find-method-name',
    fallback: 'find-method-params',
    semantics: 'method-declaration'
  },

  // 新增：模板字面量策略
  'TemplateLiteral': {
    priority: 'low',
    strategy: 'find-template-start',
    fallback: 'find-backtick',
    semantics: 'template-string'
  },
  'TaggedTemplateExpression': {
    priority: 'medium',
    strategy: 'find-tag-function',
    fallback: 'find-template-start',
    semantics: 'tagged-template'
  },

  // 新增：异步相关策略
  'AwaitExpression': {
    priority: 'medium',
    strategy: 'find-await-keyword',
    fallback: 'find-await-expression',
    semantics: 'async-operation'
  },
  'YieldExpression': {
    priority: 'medium',
    strategy: 'find-yield-keyword',
    fallback: 'find-yield-expression',
    semantics: 'generator-operation'
  }
};