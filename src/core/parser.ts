import { parseSync, type Module, type Node } from '@swc/core';
import { readFile } from 'fs/promises';
import { ParseError } from './errors';
import type { IgnoreExemption } from './types';
import { PositionConverter } from '../utils/position-converter';

export class ASTParser {
  private sourceCode: string = '';
  private sourceLines: string[] = [];
  
  /**
   * 解析TypeScript/JavaScript代码为AST
   */
  public async parseCode(code: string, filePath?: string): Promise<Module> {
    this.sourceCode = code;
    this.sourceLines = code.split('\n');
    
    try {
      // 安全的文件路径处理
      const safeFilePath = filePath || 'unknown.ts';
      
      const ast = parseSync(code, {
        syntax: this.detectSyntax(safeFilePath),
        tsx: safeFilePath.endsWith('.tsx'),
        jsx: safeFilePath.endsWith('.jsx'),
        decorators: true,
        dynamicImport: true,
        target: 'es2022'
      });
      
      return ast as Module;
    } catch (error) {
      throw new ParseError(filePath || 'unknown.ts', error as Error);
    }
  }

  /**
   * 解析文件为AST
   */
  public async parseFile(filePath: string): Promise<Module> {
    try {
      const code = await readFile(filePath, 'utf-8');
      return this.parseCode(code, filePath);
    } catch (error) {
      throw new ParseError(filePath, error as Error);
    }
  }

  /**
   * 检测文件语法类型
   */
  private detectSyntax(filePath: string): 'typescript' | 'ecmascript' {
    const tsExtensions = ['.ts', '.tsx'];
    const jsExtensions = ['.js', '.jsx', '.mjs', '.cjs'];
    
    if (tsExtensions.some(ext => filePath.endsWith(ext))) {
      return 'typescript';
    }
    
    if (jsExtensions.some(ext => filePath.endsWith(ext))) {
      return 'ecmascript';
    }
    
    // 默认使用typescript语法，因为它兼容JavaScript
    return 'typescript';
  }

  /**
   * 获取函数名称
   */
  public getFunctionName(node: any): string {
    // 处理ExportDeclaration包装的函数
    if (node.type === 'ExportDeclaration' && node.declaration) {
      return this.getFunctionName(node.declaration);
    }
    
    // 处理VariableDeclarator中的函数表达式
    if (node.type === 'VariableDeclarator' && node.id) {
      return node.id.value || node.id.name || '<anonymous>';
    }
    
    // 处理类方法（包括MethodDefinition和ClassMethod）
    if (node.type === 'MethodDefinition' || node.type === 'ClassMethod') {
      if (node.key?.value) {
        return node.key.value;
      }
      if (node.key?.type === 'Identifier') {
        return node.key.value || node.key.name;
      }
      return '<method>';
    }
    
    // 处理其他MethodDefinition
    if (node.key?.value) {
      return node.key.value;
    }
    
    if (node.key?.type === 'Identifier') {
      return node.key.value || node.key.name;
    }
    
    // 处理FunctionDeclaration
    if (node.identifier?.value) {
      return node.identifier.value;
    }
    
    if (node.identifier?.name) {
      return node.identifier.name;
    }
    
    // 匿名函数
    if (node.type === 'ArrowFunctionExpression' || node.type === 'FunctionExpression') {
      return '<anonymous>';
    }
    
    return '<anonymous>';
  }

  /**
   * 获取节点位置信息（简化版，仅调用PositionConverter）
   */
  public getLocation(node: any): { line: number; column: number } {
    // 处理包装的函数节点
    if (node.type === 'ExportDeclaration' && node.declaration) {
      return this.getLocation(node.declaration);
    }
    
    if (node.type === 'VariableDeclarator' && node.init) {
      return this.getLocation(node.init);
    }
    
    const span = node.span;
    if (span && span.start !== undefined) {
      const position = PositionConverter.spanToPosition(this.sourceCode, span.start);
      return {
        line: position.line,
        column: position.column
      };
    }
    
    return { line: 1, column: 0 };
  }

  /**
   * 检查指定行号是否被 ignore-next-line 注释豁免
   */
  public isLineIgnored(lineNumber: number): boolean {
    if (!this.sourceLines || lineNumber < 1) {
      return false;
    }
    
    // 检查前一行是否有豁免注释
    const previousLineIndex = lineNumber - 2; // 转换为0索引
    if (previousLineIndex < 0) {
      return false;
    }
    
    const previousLine = this.sourceLines[previousLineIndex];
    if (!previousLine) {
      return false;
    }
    return this.hasIgnoreComment(previousLine);
  }

  /**
   * 查找所有豁免注释
   */
  public findIgnoreExemptions(): IgnoreExemption[] {
    const exemptions: IgnoreExemption[] = [];
    
    for (let i = 0; i < this.sourceLines.length; i++) {
      const line = this.sourceLines[i];
      if (line && this.hasIgnoreComment(line)) {
        exemptions.push({
          line: i + 2, // 豁免应用到下一行(+2因为从1开始计数)
          type: 'ignore-next-line',
          complexityReduced: 0 // 将在计算器中设置
        });
      }
    }
    
    return exemptions;
  }

  /**
   * 检查字符串是否包含豁免注释
   */
  private hasIgnoreComment(line: string): boolean {
    const cleanLine = line.trim();
    
    // 支持多种注释格式
    const patterns = [
      /\/\/\s*cognitive-complexity-ignore-next-line/,
      /\/\*\s*cognitive-complexity-ignore-next-line\s*\*\//,
      /<!--\s*cognitive-complexity-ignore-next-line\s*-->/
    ];
    
    return patterns.some(pattern => pattern.test(cleanLine));
  }
}