import type { Node } from '@swc/core';
import type { AsyncRuleEngine } from '../engine/types';
import { BaseVisitor } from './base-visitor';
import { DetailCollector } from './detail-collector';
import { RuleRegistry } from './rule-registry';
import type { CalculationOptions, FunctionResult } from './types';
import { DiagnosticMarker, RuleCategory } from './types';
import { PositionConverter } from '../utils/position-converter';

// 语义感知服务
import { SemanticPositionService } from './semantic-position-service';
import { IntelligentFallbackEngine } from './intelligent-fallback-engine';
import { JSXSemanticParser } from './jsx-semantic-parser';
import { FunctionSemanticAnalyzer } from './function-semantic-analyzer';
import { CodeSemanticAnalyzer } from './code-semantic-analyzer';
import type { SemanticPositioningOptions } from './semantic-types';

/**
 * 语义感知服务依赖注入容器
 */
interface SemanticServices {
  semanticPosition: SemanticPositionService;
  fallbackEngine: IntelligentFallbackEngine;
  jsxParser: JSXSemanticParser;
  functionAnalyzer: FunctionSemanticAnalyzer;
  codeAnalyzer: CodeSemanticAnalyzer;
}

/**
 * 重构后的 ComplexityVisitor - 语义感知版本
 *
 * 核心改进：
 * - 模块化架构：职责分离到专门的语义服务
 * - 语义感知定位：精确定位到逻辑代码而非结构代码
 * - 智能回退机制：多级回退策略确保定位可靠性
 * - 可测试性：每个语义服务可独立测试
 * - 可扩展性：新增语义规则或节点类型支持更容易
 */
export class SemanticComplexityVisitor extends BaseVisitor {
  private readonly sourceCode: string;
  private readonly detailCollector?: DetailCollector;
  private readonly options: CalculationOptions;
  private readonly asyncRuleEngine?: AsyncRuleEngine;
  private readonly functionResults: Map<string, FunctionResult> = new Map();

  // 语义感知服务
  private readonly semanticServices: SemanticServices;

  // 状态管理
  private complexity = 0;
  private nestingLevel = 0;
  private currentFunction: string | null = null;
  private functionStack: Array<{ functionName: string | null; complexity: number }> = [];

  constructor(
    sourceCode: string,
    detailCollector?: DetailCollector,
    options: CalculationOptions = {},
    semanticServices?: SemanticServices,
    asyncRuleEngine?: AsyncRuleEngine
  ) {
    super();

    this.sourceCode = sourceCode;
    this.detailCollector = detailCollector;
    this.options = options;
    this.asyncRuleEngine = asyncRuleEngine;

    // 初始化语义服务
    if (semanticServices) {
      this.semanticServices = semanticServices;
    } else {
      // 创建默认语义服务
      this.semanticServices = this.createDefaultSemanticServices();
    }
  }

  /**
   * 获取函数分析结果
   * @returns 函数结果映射
   */
  getFunctionResults(): Map<string, FunctionResult> {
    return this.functionResults;
  }

  /**
   * 重写visit方法以正确处理函数边界管理和嵌套层级
   * @param node 要访问的节点
   * @returns 访问后的节点
   */
  public override visit<T extends Node>(node: T): T {
    // 检查是否是函数节点
    const isFunctionNode = this.isFunctionNode(node);

    if (isFunctionNode) {
      // 函数节点需要特殊处理：先设置函数上下文，再访问子节点
      return this.visitFunctionNode(node);
    } else {
      // 非函数节点：检查是否是嵌套节点，管理嵌套层级
      const isNesting = this.isNestingNode(node);
      const previousNestingLevel = this.nestingLevel;

      // 推入父节点栈
      this.parentStack.push(node);

      try {
        // 如果是嵌套节点，先增加嵌套层级
        if (isNesting) {
          this.nestingLevel++;
        }

        // 调用节点特定访问方法
        this.visitNode(node);

        // 访问子节点
        this.visitChildren(node);

        return node;
      } finally {
        // 恢复嵌套层级
        if (isNesting) {
          this.nestingLevel = previousNestingLevel;
        }
        // 确保栈清理
        this.parentStack.pop();
      }
    }
  }

  /**
   * 专门处理函数节点的访问
   * @param node 函数节点
   * @returns 访问后的节点
   */
  private visitFunctionNode<T extends Node>(node: T): T {
    // 推入父节点栈
    this.parentStack.push(node);

    try {
      // 获取函数信息和位置
      const functionInfo = this.semanticServices.functionAnalyzer.analyzeFunctionSemantics(node);
      const position = functionInfo.logicalStartPosition || this.getSemanticPosition(node);

      // 开始函数处理：设置函数上下文
      this.startFunction(node, position, this.extractFunctionName(node) || 'anonymous');

      // 注意：不调用 visitNode，因为函数节点不需要增加复杂度
      // 直接访问函数体的子节点（此时函数上下文已正确设置）
      this.visitChildren(node);

      // 完成函数处理：记录函数结果
      this.finishFunction(node);

      return node;
    } finally {
      // 确保栈清理
      this.parentStack.pop();
    }
  }

  /**
   * 检查节点是否是函数节点
   * @param node 节点
   * @returns 是否是函数节点
   */
  private isFunctionNode(node: any): boolean {
    if (!node || !node.type) return false;

    const functionTypes = [
      'FunctionDeclaration',
      'FunctionExpression',
      'ArrowFunctionExpression',
      'MethodDefinition',
      'ClassMethod', // SWC中的类方法
      'MethodProperty', // SWC中的对象方法简写
    ];

    return functionTypes.includes(node.type);
  }

  /**
   * 获取当前复杂度
   * @returns 复杂度值
   */
  getComplexity(): number {
    return this.complexity;
  }

  /**
   * 获取总复杂度（所有函数的复杂度之和）
   * @returns 总复杂度值
   */
  getTotalComplexity(): number {
    let total = 0;
    this.functionResults.forEach((result) => {
      total += result.complexity;
    });
    return total;
  }

  /**
   * 访问特定节点的实现
   * @param node 要访问的节点
   * @returns 处理后的节点
   */
  protected visitNode(node: Node): Node {
    // 添加详细调试信息
    if (this.options.enableDebugLog) {
      console.log(`[DEBUG] Visiting node type: "${node.type}" at span ${(node as any).span?.start || 'unknown'}`);
    }

    // 根据节点类型分发到具体的访问方法
    switch (node.type) {
      case 'IfStatement':
        if (this.options.enableDebugLog) console.log(`[DEBUG] Processing IfStatement`);
        this.visitIfStatement(node);
        break;
      case 'WhileStatement':
        this.visitWhileStatement(node);
        break;
      case 'ForStatement':
        this.visitForStatement(node);
        break;
      case 'ConditionalExpression':
        this.visitConditionalExpression(node);
        break;
      case 'LogicalExpression':
        this.visitLogicalExpression(node);
        break;
      case 'CatchClause':
        if (this.options.enableDebugLog) console.log(`[DEBUG] Processing CatchClause`);
        this.visitCatchClause(node);
        break;
      case 'JSXElement':
        this.visitJSXElement(node);
        break;
      case 'JSXExpressionContainer':
        this.visitJSXExpressionContainer(node);
        break;
      case 'ArrowFunctionExpression':
        this.visitArrowFunctionExpression(node);
        break;
      case 'FunctionExpression':
        this.visitFunctionExpression(node);
        break;
      case 'FunctionDeclaration':
        this.visitFunctionDeclaration(node);
        break;
      case 'ClassMethod':
        this.visitClassMethod(node);
        break;
      case 'MethodProperty':
        this.visitMethodProperty(node);
        break;

      // 非复杂度贡献节点 - 不增加复杂度但需要访问子节点
      case 'ObjectExpression':
      case 'ArrayExpression':
      case 'Identifier':
      case 'VariableDeclarator':
      case 'VariableDeclaration':
      case 'MemberExpression':
      case 'CallExpression':
      case 'NewExpression':
      case 'AssignmentExpression':
      case 'UpdateExpression':
      case 'UnaryExpression':
      case 'BinaryExpression':
      case 'Property':
      case 'Literal':
      case 'TemplateLiteral':
      case 'TemplateElement':
      case 'BlockStatement':
      case 'ExpressionStatement':
      case 'ReturnStatement':
      case 'BreakStatement':
      case 'ContinueStatement':
      case 'EmptyStatement':
      case 'ImportDeclaration':
      case 'ExportDeclaration':
      case 'ExportDefaultDeclaration':
      case 'ExportNamedDeclaration':
      case 'ImportDefaultSpecifier':
      case 'ImportSpecifier':
      case 'ExportSpecifier':
      case 'ThrowStatement':
      case 'TryStatement':
        // TryStatement本身不增加复杂度，但会继续访问其catch子句
        break;
      case 'SwitchStatement':
        this.visitSwitchStatement(node);
        break;
      case 'SwitchCase':
        this.visitSwitchCase(node);
        break;
      case 'DoWhileStatement':
        this.visitDoWhileStatement(node);
        break;
      case 'ForInStatement':
        this.visitForInStatement(node);
        break;
      case 'ForOfStatement':
        this.visitForOfStatement(node);
        break;
      case 'LabeledStatement':
      case 'WithStatement':
      case 'ClassDeclaration':
      case 'ClassExpression':
      case 'Super':
      case 'ThisExpression':
      case 'MetaProperty':
      case 'SpreadElement':
      case 'RestElement':
      case 'ParenthesizedExpression':
      case 'SequenceExpression':
      case 'TaggedTemplateExpression':
      case 'YieldExpression':
      case 'AwaitExpression':
      case 'ConsequentExpression':
      case 'AlternateExpression':
      // TypeScript 特定节点类型
      case 'TsTypeAnnotation':
      case 'TsUnionType':
      case 'TsIntersectionType':
      case 'TsArrayType':
      case 'TsKeywordType':
      case 'TsLiteralType':
      case 'TsTypeReference':
      case 'TsAsExpression':
      case 'TsNonNullExpression':
      case 'TsInterfaceDeclaration':
      case 'TsTypeAliasDeclaration':
      case 'TsEnumDeclaration':
      case 'TsModuleDeclaration':
      case 'TsNamespaceDeclaration':
      case 'TsExportAssignment':
      case 'TsImportEqualsDeclaration':
      // 字面量节点类型
      case 'StringLiteral':
      case 'NumericLiteral':
      case 'BooleanLiteral':
      case 'NullLiteral':
      case 'UndefinedLiteral':
      case 'BigIntLiteral':
      case 'RegExpLiteral':
      // JSX 额外节点类型
      case 'JSXOpeningElement':
      case 'JSXClosingElement':
      case 'JSXAttribute':
      case 'JSXSpreadAttribute':
      case 'JSXText':
      case 'JSXEmptyExpression':
      case 'JSXFragment':
      case 'JSXOpeningFragment':
      case 'JSXClosingFragment':
      case 'JSXMemberExpression':
      case 'JSXNamespacedName':
      // 解构和模式匹配
      case 'ObjectPattern':
      case 'ArrayPattern':
      case 'RestPattern':
      case 'AssignmentPattern':
      case 'AssignmentPatternProperty':
      case 'KeyValuePatternProperty':
      case 'KeyValueProperty':
      // ES6+ 特性
      case 'OptionalChainingExpression':
      case 'ParenthesisExpression':
      case 'ChainExpression':
      case 'MethodDefinition':
      case 'PropertyDefinition':
      case 'StaticBlock':
      case 'PrivateName':
      case 'Decorator':
      // 🔧 第二阶段修复：添加更多SWC特有的节点类型
      case 'Module': // SWC的顶级模块节点
      case 'Script': // SWC的脚本节点
      case 'Parameter': // SWC的参数节点
      case 'ObjectPatternProperty': // SWC的对象模式属性
      case 'ArrayPatternElement': // SWC的数组模式元素
      case 'BindingIdentifier': // SWC的绑定标识符
      case 'PrivateProperty': // SWC的私有属性
      case 'ComputedPropName': // SWC的计算属性名
      case 'Constructor': // SWC的构造函数
      case 'GetterProperty': // SWC的getter属性
      case 'SetterProperty': // SWC的setter属性
      case 'AssignmentOperator': // SWC的赋值操作符
      case 'BinaryOperator': // SWC的二元操作符
      case 'UnaryOperator': // SWC的一元操作符
      case 'UpdateOperator': // SWC的更新操作符
        // 这些节点不贡献复杂度，仅记录调试信息（如果启用）
        if (this.options.enableDebugLog) {
          console.log(`[DEBUG] Visiting non-complexity node: ${node.type}`);
        }
        break;

      default:
        // 处理未知节点类型 - 关键修复点
        if (this.options.enableDebugLog) {
          console.log(
            `[DEBUG] Processing unknown node type: "${node.type}" at span ${(node as any).span?.start}-${
              (node as any).span?.end
            }`
          );
          // 检查节点的实际结构
          const nodeKeys = Object.keys(node).filter((k) => k !== 'span');
          console.log(`[DEBUG] Node properties: ${nodeKeys.join(', ')}`);
        }
        this.handleUnknownNode(node);
        break;
    }

    return node;
  }

  /**
   * 访问子节点
   * @param node 节点
   */
  protected override visitChildren(node: any): void {
    // 简化实现：遍历节点属性
    for (const [key, value] of Object.entries(node)) {
      if (key === 'span' || key === 'type') continue;

      if (Array.isArray(value)) {
        for (const child of value) {
          if (child && typeof child === 'object' && child.type) {
            this.visit(child as Node);
          }
        }
      } else if (value && typeof value === 'object') {
        // 检查是否有type属性（标准AST节点）
        if ((value as any).type) {
          this.visit(value as Node);
        }
        // 特殊处理：ClassMethod的function属性包含函数体
        else if (key === 'function' && node.type === 'ClassMethod' && (value as any).body) {
          this.visit((value as any).body as Node);
        }
      }
    }
  }

  /**
   * 访问各种节点类型 - 使用语义感知定位
   */

  visitIfStatement(node: any): void {
    // 🔧 SWC解析器错误修复：验证IfStatement是否真的是条件语句
    if (!this.isValidIfStatement(node)) {
      if (this.options.enableDebugLog) {
        console.log(`[DEBUG] 跳过无效的IfStatement节点 at span ${node.span?.start}-${node.span?.end}`);
        const actualContent = this.sourceCode.slice(node.span.start, Math.min(node.span.end, node.span.start + 50));
        console.log(`[DEBUG] 实际内容: "${actualContent}"`);
      }
      return;
    }

    this.processComplexityNode(node, 'if-statement', '条件语句');
  }

  visitWhileStatement(node: any): void {
    this.processComplexityNode(node, 'while-statement', 'while 循环');
  }

  visitForStatement(node: any): void {
    this.processComplexityNode(node, 'for-statement', 'for 循环');
  }

  visitConditionalExpression(node: any): void {
    this.processComplexityNode(node, 'conditional-expression', '三元操作符');
  }

  visitLogicalExpression(node: any): void {
    this.processComplexityNode(node, 'logical-operator', '逻辑操作符');
  }

  visitCatchClause(node: any): void {
    this.processComplexityNode(node, 'catch-clause', 'catch 异常处理');
  }

  visitJSXElement(node: any): void {
    // JSX 元素使用专门的语义解析
    const jsxInfo = this.semanticServices.jsxParser.parseJSXElement(node);

    if (jsxInfo.hasLogicalContent && jsxInfo.logicalComplexity > 0) {
      const position = this.getSemanticPosition(node);
      this.addComplexity(jsxInfo.logicalComplexity, position, 'jsx-element', 'JSX 逻辑复杂度', node.type, node.span);
    }
  }

  visitJSXExpressionContainer(node: any): void {
    // JSX 表达式容器 - 只有包含逻辑复杂度的表达式才计算复杂度
    const jsxInfo = this.semanticServices.jsxParser.parseJSXElement(node);

    // 只有当表达式包含真正的逻辑复杂度时才加分
    if (jsxInfo.logicalComplexity > 0) {
      const expressionPosition = this.semanticServices.jsxParser.findJSXExpressionContent(node);
      const position = expressionPosition || this.getSemanticPosition(node);
      this.addComplexity(jsxInfo.logicalComplexity, position, 'jsx-expression', 'JSX 逻辑复杂度', node.type, node.span);
    }

    // 简单的表达式如 {t('...')} 或 {variable} 不增加复杂度
  }

  visitArrowFunctionExpression(_node: any): void {
    // 箭头函数的访问将通过 visitFunctionNode 处理
    // 这里只需要处理非函数特有的逻辑
  }

  visitFunctionExpression(_node: any): void {
    // 函数表达式的访问将通过 visitFunctionNode 处理
    // 这里只需要处理非函数特有的逻辑
  }

  visitFunctionDeclaration(_node: any): void {
    // 函数声明的访问将通过 visitFunctionNode 处理
    // 这里只需要处理非函数特有的逻辑
  }

  visitClassMethod(_node: any): void {
    // 类方法的访问将通过 visitFunctionNode 处理
    // 这里只需要处理非函数特有的逻辑
  }

  visitMethodProperty(_node: any): void {
    // 对象方法的访问将通过 visitFunctionNode 处理
    // 这里只需要处理非函数特有的逻辑
  }

  /**
   * 🔧 第二阶段修复：添加缺失的AST节点visit方法
   */

  visitSwitchStatement(node: any): void {
    this.processComplexityNode(node, 'switch-statement', 'switch 语句');
  }

  visitSwitchCase(node: any): void {
    // SwitchCase 通常不单独计算复杂度，复杂度由其中的逻辑贡献
    // 但如果每个case都增加1复杂度的话，可以启用下面的代码：
    // this.processComplexityNode(node, 'switch-case', 'switch case');
  }

  visitDoWhileStatement(node: any): void {
    this.processComplexityNode(node, 'do-while-statement', 'do-while 循环');
  }

  visitForInStatement(node: any): void {
    this.processComplexityNode(node, 'for-in-statement', 'for-in 循环');
  }

  visitForOfStatement(node: any): void {
    this.processComplexityNode(node, 'for-of-statement', 'for-of 循环');
  }

  /**
   * 开始函数处理 - 设置函数上下文
   * @param _node 函数节点
   * @param position 位置
   * @param functionName 函数名
   */
  private startFunction(_node: any, position: number, functionName: string): void {
    const lineInfo = this.calculateLineNumber(position);

    // 启动详细信息收集器的函数跟踪
    if (this.detailCollector && functionName) {
      this.detailCollector.startFunction(functionName, lineInfo.line, lineInfo.column);
    }

    // 保存当前状态（支持嵌套函数）
    const previousFunction = this.currentFunction;
    const previousComplexity = this.complexity;

    // 重置函数级状态
    this.currentFunction = functionName;
    this.complexity = 0;
    this.nestingLevel = 0;

    // 保存之前的状态以便恢复（嵌套函数支持）
    this.functionStack = this.functionStack || [];
    this.functionStack.push({
      functionName: previousFunction,
      complexity: previousComplexity,
    });
  }

  /**
   * 完成函数处理 - 在所有子节点访问完成后调用
   * @param node 函数节点
   */
  private finishFunction(node: any): void {
    const functionName = this.currentFunction;

    if (!functionName) {
      console.warn('finishFunction called but no currentFunction set');
      return;
    }

    // 结束详细信息收集并获取详情
    let functionDetails: any = undefined;
    if (this.detailCollector) {
      try {
        functionDetails = this.detailCollector.endFunction();
      } catch (error) {
        // 如果结束函数失败，记录错误但继续
        console.warn(`Failed to end function tracking for ${functionName}:`, error);
      }
    }

    // 计算函数位置
    const functionInfo = this.semanticServices.functionAnalyzer.analyzeFunctionSemantics(node);
    const position = functionInfo.logicalStartPosition || this.getSemanticPosition(node);
    const lineInfo = this.calculateLineNumber(position);

    // 记录函数结果
    this.functionResults.set(functionName, {
      name: functionName,
      complexity: this.complexity,
      line: lineInfo.line,
      column: lineInfo.column,
      filePath: '', // 将在外部设置
      details: functionDetails?.details || [], // 修正：使用details而不是steps
    });

    // 恢复之前的状态（支持嵌套函数）
    const previousState = this.functionStack.pop();
    if (previousState) {
      this.currentFunction = previousState.functionName;
      this.complexity = previousState.complexity;
    } else {
      // 没有更多嵌套函数，回到顶层状态
      this.currentFunction = null;
      this.complexity += this.functionResults.get(functionName)?.complexity || 0;
    }
  }

  /**
   * 处理复杂度节点 - 核心语义感知处理（简化版本）
   * @param node AST 节点
   * @param ruleId 规则ID
   * @param description 描述
   */
  private processComplexityNode(node: any, ruleId: string, description: string): void {
    // 获取语义感知位置
    const position = this.getSemanticPosition(node);

    // 获取规则复杂度
    const baseComplexity = this.getRuleComplexity(ruleId);

    // 计算嵌套加成
    const nestingIncrement = this.getNestingIncrement();
    const totalComplexity = baseComplexity + nestingIncrement;

    // 获取 span 信息
    const span = node.span ? { start: node.span.start, end: node.span.end } : undefined;

    // 记录复杂度，传递节点类型和span信息用于智能位置修正
    this.addComplexity(totalComplexity, position, ruleId, description, node.type, span);

    // 注意：不在这里处理子节点访问或嵌套层级管理
    // 这些都由统一的 visit 方法处理
  }

  /**
   * 获取语义感知位置 - 核心定位逻辑
   * @param node AST 节点
   * @returns 语义位置
   */
  private getSemanticPosition(node: any): number {
    // 尝试从 span 获取回退位置
    const fallbackSpan = node.span?.start;

    // 使用语义位置服务进行智能定位
    const semanticPosition = this.semanticServices.semanticPosition.findSemanticPosition(node, fallbackSpan);

    // 如果语义定位失败，使用智能回退引擎
    if (semanticPosition === fallbackSpan && fallbackSpan !== undefined) {
      const fallbackResult = this.semanticServices.fallbackEngine.performIntelligentFallback(node);
      return fallbackResult.position || fallbackSpan;
    }

    return semanticPosition;
  }

  /**
   * 添加复杂度
   * @param complexity 复杂度值
   * @param position 位置
   * @param ruleId 规则ID
   * @param description 描述
   * @param nodeType 节点类型（用于智能位置修正）
   * @param span SWC span 信息（可选）
   */
  private addComplexity(
    complexity: number,
    position: number,
    ruleId: string,
    description: string,
    nodeType?: string,
    span?: { start: number; end: number }
  ): void {
    // 确保nodeType有效，添加强壮的fallback处理
    const safeNodeType = nodeType || 'UnresolvedNode';

    this.complexity += complexity;

    // 改进：即使没有活跃函数，也记录详细信息（用于调试）
    if (this.detailCollector) {
      const lineInfo = this.calculateLineNumber(position, safeNodeType);

      // 如果没有当前函数，创建一个临时的全局上下文
      if (!this.currentFunction) {
        // 尝试启动一个全局函数上下文
        const globalFunctionName = '<global>';
        this.detailCollector.startFunction(globalFunctionName, lineInfo.line, lineInfo.column);
        this.currentFunction = globalFunctionName;
      }

      this.detailCollector.addStep({
        line: lineInfo.line,
        column: lineInfo.column,
        increment: complexity,
        ruleId: ruleId,
        description,
        context: this.buildContext(description),
        nodeType: safeNodeType,
        span: span,
      });
    }

    // 调试信息：记录复杂度归属
    if (this.options.enableDebugLog) {
      console.log(
        `[DEBUG] Added complexity ${complexity} to function "${this.currentFunction || '<none>'}" at line ${
          this.calculateLineNumber(position, safeNodeType).line
        }: ${description}`
      );
    }
  }

  /**
   * 根据字符位置计算行号和列号（改进版本）
   * 使用PositionConverter的标准转换逻辑，确保与其他组件一致
   * @param position 字符位置
   * @param nodeType 节点类型（用于智能位置修正）
   * @returns 行号和列号信息
   */
  private calculateLineNumber(position: number, nodeType?: string): { line: number; column: number } {
    if (position <= 0) {
      return { line: 1, column: 1 };
    }

    if (position >= this.sourceCode.length) {
      // 位置超出源代码范围，使用最后一行
      const lines = this.sourceCode.split('\n');
      const lastLine = lines[lines.length - 1];
      return { line: lines.length, column: (lastLine?.length || 0) + 1 };
    }

    // 使用智能位置转换，包含回退机制和节点类型修正
    try {
      return PositionConverter.spanToPosition(this.sourceCode, position, undefined, undefined, nodeType);
    } catch (_error) {
      // 后备方案：使用基础转换
      try {
        return PositionConverter.fastSpanToPosition(this.sourceCode, position);
      } catch (_fallbackError) {
        // 最终后备：逐字符计算
        let line = 1;
        let column = 1;

        for (let i = 0; i < position && i < this.sourceCode.length; i++) {
          if (this.sourceCode[i] === '\n') {
            line++;
            column = 1;
          } else {
            column++;
          }
        }

        return { line, column };
      }
    }
  }

  /**
   * 获取规则复杂度
   * @param ruleId 规则ID
   * @returns 复杂度值
   */
  private getRuleComplexity(ruleId: string): number {
    if (this.asyncRuleEngine) {
      // 异步规则引擎处理（简化）
      return 1;
    }

    // 从规则注册表获取
    const rule = RuleRegistry.getRule(ruleId);
    return rule ? 1 : 1; // 简化为固定值
  }

  /**
   * 获取嵌套增量
   * @returns 嵌套增量
   */
  private getNestingIncrement(): number {
    return this.nestingLevel;
  }

  /**
   * 检查是否为嵌套节点
   * @param node AST 节点
   * @returns 是否嵌套
   */
  private isNestingNode(node: any): boolean {
    const nestingTypes = ['IfStatement', 'WhileStatement', 'ForStatement', 'TryStatement', 'SwitchStatement'];

    return nestingTypes.includes(node.type);
  }

  /**
   * 在嵌套上下文中执行
   * @param callback 回调函数
   */
  private withNesting(callback: () => void): void {
    this.nestingLevel++;
    try {
      callback();
    } finally {
      this.nestingLevel--;
    }
  }

  /**
   * 提取函数名 - 改进版本
   * @param node 函数节点
   * @returns 函数名
   */
  private extractFunctionName(node: any): string {
    // 处理命名函数声明 - SWC使用 identifier.value
    if (node.identifier?.value) {
      return node.identifier.value;
    }

    // 处理命名函数声明 - 备用检查
    if ((node as any).id?.name) {
      return (node as any).id.name;
    }

    if ((node as any).id?.value) {
      return (node as any).id.value;
    }

    // 处理类方法和对象方法
    if ((node.type === 'ClassMethod' || node.type === 'MethodProperty') && (node as any).key) {
      const key = (node as any).key;
      if (key.name) {
        return key.name;
      }
      if (key.value) {
        return key.value;
      }
      if (key.type === 'Identifier') {
        return key.name || key.value || 'method';
      }
    }

    // 处理传统方法定义
    if (node.type === 'MethodDefinition' && (node as any).key) {
      const key = (node as any).key;
      if (key.name) {
        return key.name;
      }
      if (key.value) {
        return key.value;
      }
      if (key.type === 'Identifier') {
        return key.name || key.value || 'method';
      }
    }

    // 处理赋值给变量的函数表达式
    // 检查父节点是否是变量声明或赋值表达式
    const parent = this.getParent();
    if (parent) {
      // const/let/var functionName = function() {}
      if (parent.type === 'VariableDeclarator' && (parent as any).id) {
        const id = (parent as any).id;
        if (id.name) {
          return id.name;
        }
        if (id.value) {
          return id.value;
        }
      }

      // obj.methodName = function() {}
      if (parent.type === 'AssignmentExpression' && (parent as any).left) {
        const left = (parent as any).left;
        if (left.type === 'MemberExpression' && left.property) {
          // SWC中属性名可能在name或value中
          return left.property.name || left.property.value || 'method';
        }
        if (left.type === 'Identifier') {
          return left.name || left.value || 'assignment';
        }
      }

      // 对象字面量中的方法 { methodName: function() {} }
      if (parent.type === 'KeyValueProperty' && (parent as any).key) {
        const key = (parent as any).key;
        if (key.type === 'Identifier') {
          return key.name || key.value || 'property';
        }
        // 如果key是字符串字面量
        if (key.type === 'StringLiteral') {
          return key.value;
        }
      }

      // 对象方法简写 { methodName() {} } - 兼容旧API
      if (parent.type === 'Property' && (parent as any).key) {
        const key = (parent as any).key;
        if (key.name) {
          return key.name;
        }
        if (key.value) {
          return key.value;
        }
      }
    }

    // 特殊处理箭头函数
    if (node.type === 'ArrowFunctionExpression') {
      const lineInfo = this.calculateLineNumber(this.getSemanticPosition(node));
      return `arrow_${lineInfo.line}_${lineInfo.column}`;
    }

    // 匿名函数 - 使用更友好的命名
    const lineInfo = this.calculateLineNumber(this.getSemanticPosition(node));
    return `anonymous_${lineInfo.line}_${lineInfo.column}`;
  }

  /**
   * 构建上下文信息
   * @param description 描述
   * @returns 上下文
   */
  private buildContext(description: string): string {
    const nestingInfo = this.nestingLevel > 0 ? ` (嵌套层级: ${this.nestingLevel})` : '';
    return `${description}${nestingInfo}`;
  }

  /**
   * 创建默认语义服务
   * @returns 语义服务
   */
  private createDefaultSemanticServices(): SemanticServices {
    const options: SemanticPositioningOptions = {
      enableJSXSemantics: true,
      enableFunctionSemantics: true,
      enableSmartLineFiltering: true,
    };

    const codeAnalyzer = new CodeSemanticAnalyzer(this.sourceCode);
    const jsxParser = new JSXSemanticParser(this.sourceCode, codeAnalyzer);
    const functionAnalyzer = new FunctionSemanticAnalyzer(this.sourceCode, codeAnalyzer);
    const semanticPosition = new SemanticPositionService(this.sourceCode, codeAnalyzer, options);
    const fallbackEngine = new IntelligentFallbackEngine(semanticPosition, codeAnalyzer, this.sourceCode);

    return {
      semanticPosition,
      fallbackEngine,
      jsxParser,
      functionAnalyzer,
      codeAnalyzer,
    };
  }

  /**
   * 🔧 SWC解析器错误修复：验证IfStatement是否真的是条件语句
   * @param node 被识别为IfStatement的AST节点
   * @returns 是否是有效的If语句
   */
  private isValidIfStatement(node: any): boolean {
    // 检查基本结构 - 真正的IfStatement应该有test属性
    if (!node.test) {
      if (this.options.enableDebugLog) {
        console.log(`[DEBUG] IfStatement验证失败: 没有test属性`);
      }
      return false;
    }

    // 检查test条件是否合理 - 主要检查点
    if (node.test && node.test.span && this.sourceCode) {
      const testStart = node.test.span.start;
      const testEnd = node.test.span.end;

      // 验证span范围是否合法
      if (testStart < 0 || testEnd > this.sourceCode.length || testStart >= testEnd) {
        if (this.options.enableDebugLog) {
          console.log(`[DEBUG] IfStatement验证失败: 非法的test span ${testStart}-${testEnd}`);
        }
        return false;
      }

      const testContent = this.sourceCode.slice(testStart, testEnd);

      // 检查条件是否包含明显的非条件内容（如对象属性）
      const suspiciousPatterns = [
        /^\s*[a-zA-Z_$][a-zA-Z0-9_$]*\s*:/, // 对象属性模式 "key:"
        /^[^()]*:\s*\{/, // 对象属性后跟对象字面量
        /^\s*exportParams\s*:/, // 特定的fireExport问题模式
        /^\s*bizType\s*,/, // 特定的bizType问题模式
      ];

      for (const pattern of suspiciousPatterns) {
        if (pattern.test(testContent)) {
          if (this.options.enableDebugLog) {
            console.log(`[DEBUG] IfStatement验证失败: 条件包含可疑内容: "${testContent}"`);
          }
          return false;
        }
      }
    }

    // 检查实际源码内容 - 次要检查点
    if (node.span && this.sourceCode) {
      const spanStart = node.span.start;

      // 验证span范围是否合法
      if (spanStart < 0 || spanStart >= this.sourceCode.length) {
        if (this.options.enableDebugLog) {
          console.log(`[DEBUG] IfStatement验证失败: 非法的span起始位置 ${spanStart}`);
        }
        return false;
      }

      // 向前查找实际的if关键字（在合理范围内）
      let foundIf = false;
      for (let i = Math.max(0, spanStart - 50); i <= Math.min(this.sourceCode.length - 2, spanStart + 50); i++) {
        if (
          this.sourceCode.slice(i, i + 2) === 'if' &&
          (i === 0 || /\s/.test(this.sourceCode[i - 1])) && // 前面是空白或开始
          (i + 2 >= this.sourceCode.length || /\s|\(/.test(this.sourceCode[i + 2]))
        ) {
          // 后面是空白或(
          foundIf = true;
          break;
        }
      }

      if (!foundIf) {
        if (this.options.enableDebugLog) {
          const contextStart = Math.max(0, spanStart - 20);
          const contextEnd = Math.min(this.sourceCode.length, spanStart + 30);
          const context = this.sourceCode.slice(contextStart, contextEnd);
          console.log(`[DEBUG] IfStatement验证失败: 附近未找到'if'关键字，上下文: "${context}"`);
        }
        return false;
      }
    }

    return true;
  }

  /**
   * 处理未知节点类型 - 关键修复方法
   * @param node AST 节点
   */
  private handleUnknownNode(node: any): void {
    // 记录未知节点类型用于调试和改进
    if (this.options.enableDebugLog || this.detailCollector) {
      // 🔧 第一阶段修复：确保使用起始位置
      const position = node.span?.start || 0;
      const lineInfo = this.calculateLineNumber(position);

      // 修正：正确显示节点类型而不是span内容
      const debugMessage = `UnknownNode@L${lineInfo.line}:C${lineInfo.column}: "${node.type}"`;

      if (this.options.enableDebugLog) {
        console.log(`[DEBUG] ${debugMessage}`);
      }

      // 为DetailCollector添加未知节点信息，但不增加复杂度
      if (this.detailCollector && this.currentFunction) {
        // 不调用 addStep，仅记录诊断信息
        console.warn(`📊 遇到未知节点类型: ${node.type} 在 ${debugMessage}`);
      }
    }

    // 🔧 第一阶段修复：移除错误的复杂度添加逻辑
    // 对于未知节点，我们不应该添加任何复杂度
    // 这避免了将普通节点（如CallExpression、Identifier）误判为复杂度贡献节点

    // 记录可疑节点类型仅用于开发时调试
    const suspiciousNodeTypes = [
      'SwitchStatement', // Switch语句应该贡献复杂度
      'DoWhileStatement', // Do-while循环应该贡献复杂度
      'ForInStatement', // for-in循环应该贡献复杂度
      'ForOfStatement', // for-of循环应该贡献复杂度
    ];

    if (suspiciousNodeTypes.includes(node.type)) {
      console.warn(`⚠️  可疑节点类型 "${node.type}" 可能需要添加到 visitNode 的 switch 语句中`);
      // 注意：不再调用 processComplexityNode，这是错误的！
      // 应该通过在 visitNode 中添加对应的 case 来处理
    }

    // 对于真正未知的节点类型，我们不增加复杂度
    // 这是保守的做法，避免误报
  }

  /**
   * 判断节点是否应该贡献复杂度 - 辅助验证方法
   * @param node AST 节点
   * @returns 是否贡献复杂度
   */
  private isComplexityContributingNode(node: any): boolean {
    const complexityNodeTypes = [
      'IfStatement',
      'WhileStatement',
      'ForStatement',
      'DoWhileStatement',
      'ForInStatement',
      'ForOfStatement',
      'ConditionalExpression',
      'LogicalExpression',
      'CatchClause',
      'SwitchStatement',
      'SwitchCase',
      'JSXElement', // JSX可能贡献复杂度（取决于内容）
      'JSXExpressionContainer', // JSX表达式容器可能贡献复杂度
    ];

    return complexityNodeTypes.includes(node.type);
  }
}
