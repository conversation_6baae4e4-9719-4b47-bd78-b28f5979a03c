/**
 * 最小化 SWC Span 调试信息工具
 * 生成紧凑的单行调试信息，优化 token 使用效率
 */

import chalk from 'chalk';
import { getGlobalFileCache } from './file-cache';
import { type DetailStep } from '../core/types';
import type { CLIOptions } from '../config/types';

/**
 * 安全转义代码片段
 */
function escapeSnippet(snippet: string, maxLength = 20): string {
  if (!snippet) return '';

  const escaped = snippet.replace(/\n/g, '\\n').replace(/\t/g, ' ').replace(/\r/g, '');

  return escaped.length > maxLength ? escaped.slice(0, maxLength - 3) + '...' : escaped;
}

/**
 * 生成最小化调试信息 - 单行格式
 * 格式: [DEBUG] NodeType@start-end: "code snippet" 或 [DEBUG] NodeType@L:C (无span)
 */
export async function formatSpanDebugInfo(
  step: DetailStep,
  filePath: string,
  options?: CLIOptions
): Promise<string | null> {
  if (!step) {
    return null;
  }

  const nodeType = step.nodeType || 'UnknownNode';
  
  // 如果有 span 信息，使用完整格式
  if (step.span && step.span.start !== undefined && step.span.end !== undefined) {
    const { start, end } = step.span;

    // 获取代码片段
    let snippet = '';
    try {
      const fileCache = getGlobalFileCache();
      const sourceCode = await fileCache.getFileContent(filePath);

      if (start >= 0 && end <= sourceCode.length && start < end) {
        snippet = escapeSnippet(sourceCode.slice(start, end));
      }
    } catch {
      // 忽略文件读取错误，使用空代码片段
    }

    // 单行紧凑格式
    const spanInfo = `${nodeType}@${start}-${end}`;
    const codeInfo = snippet ? `: "${snippet}"` : '';

    return chalk.gray(`[DEBUG] ${spanInfo}${codeInfo}`);
  } 
  
  // 如果没有 span 信息，显示行列信息和节点类型
  else {
    const position = `L${step.line}:C${step.column}`;
    const positionInfo = `${nodeType}@${position}`;
    
    // 尝试从文件中获取该行的代码片段作为参考
    let lineSnippet = '';
    try {
      const fileCache = getGlobalFileCache();
      const sourceCode = await fileCache.getFileContent(filePath);
      const lines = sourceCode.split('\n');
      
      if (step.line > 0 && step.line <= lines.length) {
        const lineContent = lines[step.line - 1] || '';
        lineSnippet = escapeSnippet(lineContent.trim(), 30);
      }
    } catch {
      // 忽略文件读取错误
    }
    
    const codeInfo = lineSnippet ? `: "${lineSnippet}"` : ' (无span信息)';
    
    return chalk.gray(`[DEBUG] ${positionInfo}${codeInfo}`);
  }
}
