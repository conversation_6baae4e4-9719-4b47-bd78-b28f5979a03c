import { analyzeCode } from './src/index.js';

// 测试具体的问题模式
const testCode = `fireExport({
  exportParams: {
    bizType,
  }
});`;

console.log("🔍 直接测试问题代码模式");

try {
  const result = await analyzeCode(testCode, 'test.tsx', {
    details: true,
    outputType: 'text',
    debug: true,
    enableDebugLog: true
  });
  
  console.log("结果:", JSON.stringify(result, null, 2));
  
  if (result.complexity > 0) {
    console.log("❌ 检测到复杂度误报！应该为0但实际为:", result.complexity);
  } else {
    console.log("✅ 复杂度正确为0");
  }
  
} catch (error) {
  console.error("错误:", error);
}