import { analyzeCode } from './src/index';

// 带函数的原始问题代码
const codeWithFunction = `
function handleExport() {
  fireExport({
    exportParams: {
      bizType,
      jobName: 'NDR',
    }
  });
}

function processData(items) {
  if (items.length > 0) {    // 这个应该有 +1 复杂度
    return items.filter(item => item.active);
  }
  return [];
}
`;

async function testWithFunction() {
  console.log('🎯 测试包含函数的代码...\n');
  
  try {
    const result = await analyzeCode(codeWithFunction, 'with-function.tsx', {
      enableDebugLog: false,
      includeDetails: true,
      enableShowContext: true
    });
    
    console.log('📊 分析结果:');
    console.log(`总复杂度: ${result.complexity}`);
    console.log(`函数数量: ${result.functions.length}`);
    
    if (result.functions.length > 0) {
      result.functions.forEach(func => {
        console.log(`\n🔧 函数: ${func.name}`);
        console.log(`  复杂度: ${func.complexity}`);
        console.log(`  位置: L${func.line}:C${func.column}`);
        
        if (func.details && func.details.length > 0) {
          console.log('  详细步骤:');
          func.details.forEach((detail, index) => {
            console.log(`    ${index + 1}. L${detail.line}:C${detail.column} +${detail.increment} - ${detail.description} (${detail.ruleId})`);
          });
        } else {
          console.log('  无复杂度贡献');
        }
      });
    }
    
    console.log('\n🎯 验证结果:');
    console.log('- handleExport函数应该复杂度为0 (无复杂度贡献的语句)');
    console.log('- processData函数应该复杂度为1 (一个if语句)');
    console.log('- 总复杂度应该为1');
    
    const handleExportFunc = result.functions.find(f => f.name === 'handleExport');
    const processDataFunc = result.functions.find(f => f.name === 'processData');
    
    if (handleExportFunc?.complexity === 0 && processDataFunc?.complexity === 1) {
      console.log('✅ 测试通过！复杂度计算正确');
    } else {
      console.log('❌ 测试失败，复杂度计算异常');
    }
    
  } catch (error) {
    console.error('❌ 分析失败:', error);
  }
}

testWithFunction();