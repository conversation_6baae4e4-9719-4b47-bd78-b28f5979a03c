/**
 * 调试processComplexityNode为什么没被调用
 */

import { analyzeCode } from './src/index';
import * as fs from 'fs';

async function debugProcessComplexityNode() {
  console.log('🔍 调试processComplexityNode调用情况\n');
  
  try {
    const filePath = '/Users/<USER>/projj/git.imile.com/ux/ds-web/apps/ds/src/pages/InventoryEE/NDR/index.tsx';
    const sourceCode = fs.readFileSync(filePath, 'utf-8');
    
    console.log('=== 真实文件if语句扫描 ===');
    
    // 简单字符串扫描找if语句
    const lines = sourceCode.split('\n');
    const realIfLines: Array<{lineNum: number, line: string}> = [];
    
    lines.forEach((line, index) => {
      if (line.trim().match(/^\s*if\s*\(/)) {
        realIfLines.push({
          lineNum: index + 1,
          line: line.trim()
        });
      }
    });
    
    console.log(`📝 真实文件中找到 ${realIfLines.length} 个if语句:`);
    realIfLines.forEach((item, index) => {
      console.log(`${index + 1}. L${item.lineNum}: "${item.line}"`);
    });
    
    console.log('\n=== 运行复杂度分析器 ===');
    const result = await analyzeCode(sourceCode, filePath, {
      enableDetails: true,
      enableDebugLog: true
    });
    
    console.log('\n=== 结果检查 ===');
    let foundIfComplexity = 0;
    result.functions.forEach((fn) => {
      console.log(`\n🔧 ${fn.name} (复杂度: ${fn.complexity})`);
      if (fn.details) {
        fn.details.forEach((step, index) => {
          console.log(`  ${index + 1}. L${step.line}:C${step.column} - ${step.description} (+${step.increment})`);
          if (step.ruleId === 'if-statement' || step.description.includes('条件语句')) {
            foundIfComplexity++;
          }
        });
      }
    });
    
    console.log(`\n📊 统计:`)
    console.log(`- 真实文件中的if语句: ${realIfLines.length} 个`)
    console.log(`- 分析器检测到的if复杂度: ${foundIfComplexity} 个`)
    
    if (realIfLines.length > foundIfComplexity) {
      console.log(`\n⚠️  有 ${realIfLines.length - foundIfComplexity} 个if语句没有被检测到！`)
      console.log('这说明visitIfStatement或processComplexityNode没有被正确调用')
    }
    
  } catch (error) {
    console.error('调试失败:', error);
  }
}

debugProcessComplexityNode();