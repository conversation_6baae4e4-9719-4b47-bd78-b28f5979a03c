import { analyzeCode } from './src/index';

// 创建一个与真实文件结构相似的测试
const testCode = `
const handleExport = (type: 'selected' | 'all') => {
  const exportParams = handleExportParams(formatedQueryParams)
  
  if (type === 'selected') {    
    fireExport({              
      exportParams: {         
        bizType,              
        jobName: 'NDR',
        queryContent: {
          ...exportParams
        }
      }
    })
  } else {
    fireExport({              
      exportParams: {         
        bizType,              
        jobName: 'NDR',
        queryContent: { ...exportParams },
      }
    })
  }
}
`;

async function testSpanPositioning() {
  console.log("🔍 测试span定位修复问题");

  try {
    const result = await analyzeCode(testCode, 'test.tsx', {
      enableDetails: true,
      enableDebugLog: true
    });
  
  console.log("结果:", JSON.stringify(result, null, 2));
  
  // 分析详细信息
  if (result.functions && result.functions[0]?.details) {
    console.log("\n📊 详细步骤分析:");
    result.functions[0].details.forEach((detail, index) => {
      console.log(`步骤${index + 1}: L${detail.line}:C${detail.column} - ${detail.description} (+${detail.increment}) [${detail.ruleId}]`);
      
      // 检查是否有误报
      if (detail.ruleId === 'if-statement' && (detail.line === 4 || detail.line === 6)) {
        console.log(`  ❌ 疑似误报: fireExport或bizType被错误识别为条件语句`);
      }
    });
  }
  
  // 检查if语句的预期位置（第5行）
  console.log("\n🎯 预期: if语句应该在L5 'if (type === 'selected')'，复杂度+1或+2");
  console.log("🔍实际: 检查fireExport位置是否错误地被标记为条件语句");
  
  // 验证修复结果
  let hasValidIfStatement = false;
  let hasFireExportFalsePositive = false;
  let hasBizTypeFalsePositive = false;
  
  if (result.functions && result.functions[0]?.details) {
    result.functions[0].details.forEach(detail => {
      if (detail.ruleId === 'if-statement') {
        if (detail.line === 5) {  // 修正：if语句在第5行
          hasValidIfStatement = true;
        } else {
          // 检查是否有其他行被误报为if语句
          if (detail.description.includes('fireExport') || detail.context?.includes('fireExport')) {
            hasFireExportFalsePositive = true;
          }
          if (detail.description.includes('bizType') || detail.context?.includes('bizType')) {
            hasBizTypeFalsePositive = true;
          }
        }
      }
    });
  }
  
  console.log("\n🔬 修复验证结果:");
  console.log(`✅ 正确识别if语句: ${hasValidIfStatement ? '是' : '否'}`);
  console.log(`❌ fireExport误报: ${hasFireExportFalsePositive ? '是' : '否'}`);
  console.log(`❌ bizType误报: ${hasBizTypeFalsePositive ? '是' : '否'}`);
  
  if (hasValidIfStatement && !hasFireExportFalsePositive && !hasBizTypeFalsePositive) {
    console.log("\n🎉 修复成功: span定位问题已解决!");
  } else {
    console.log("\n❌ 修复失败: 仍存在定位问题");
  }
  
} catch (error) {
  console.error("错误:", error);
}
}

// 运行测试
testSpanPositioning();