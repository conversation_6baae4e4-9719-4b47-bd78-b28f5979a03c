/**
 * 调试span位置转换问题
 */

import * as fs from 'fs';
import { PositionConverter } from './src/utils/position-converter';

function debugSpanPositioning() {
  console.log('🔍 调试span位置转换问题...\n');
  
  try {
    const filePath = '/Users/<USER>/projj/git.imile.com/ux/ds-web/apps/ds/src/pages/InventoryEE/NDR/index.tsx';
    const sourceCode = fs.readFileSync(filePath, 'utf-8');
    
    // 问题spans
    const problemSpans = [6799, 6836]; // if语句的spans
    
    problemSpans.forEach(span => {
      console.log(`📍 Span ${span}:`);
      
      // 转换为行列位置
      const position = PositionConverter.fastSpanToPosition(sourceCode, span);
      console.log(`  转换结果: L${position.line}:C${position.column}`);
      
      // 获取该行内容
      try {
        const lineContent = PositionConverter.getLineContent(sourceCode, position.line);
        console.log(`  行内容: "${lineContent.trim()}"`);
      } catch (error) {
        console.log(`  获取行内容失败: ${error}`);
      }
      
      // 获取实际的字符
      const actualChar = sourceCode[span];
      const surroundingText = sourceCode.slice(Math.max(0, span - 20), span + 20);
      console.log(`  实际字符: "${actualChar}"`);
      console.log(`  周围文本: "${surroundingText}"`);
      console.log();
    });
    
    // 检查fireExport和bizType行的实际位置
    const lines = sourceCode.split('\n');
    console.log('📋 目标行内容:');
    console.log(`L243: "${lines[242] || 'N/A'}"`);
    console.log(`L245: "${lines[244] || 'N/A'}"`);
    
    // 找到这些行的字节偏移量
    let offset = 0;
    for (let i = 0; i < lines.length && i < 243; i++) {
      if (i === 242) { // L243 (0-based是242)
        console.log(`\nL243的字节偏移量: ${offset}`);
        break;
      }
      offset += lines[i].length + 1; // +1 for newline
    }
    
  } catch (error) {
    console.error('调试失败:', error);
  }
}

debugSpanPositioning();