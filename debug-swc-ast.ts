import { parseSync } from '@swc/core';
import * as fs from 'fs';

function debugSwcAst() {
  console.log('🔍 调试SWC AST解析结果...\n');
  
  try {
    const filePath = '/Users/<USER>/projj/git.imile.com/ux/ds-web/apps/ds/src/pages/InventoryEE/NDR/index.tsx';
    const sourceCode = fs.readFileSync(filePath, 'utf-8');
    
    // 解析AST
    const ast = parseSync(sourceCode, {
      syntax: 'typescript',
      tsx: true,
      comments: false,
    });
    
    // 查找span范围在6780-6820之间的AST节点
    const targetSpanRange = { start: 6780, end: 6820 };
    const foundNodes: any[] = [];
    
    function visitNode(node: any, path: string = 'root') {
      if (!node || typeof node !== 'object') return;
      
      // 检查span是否在目标范围内
      if (node.span && 
          node.span.start >= targetSpanRange.start && 
          node.span.start <= targetSpanRange.end) {
        foundNodes.push({
          type: node.type,
          span: node.span,
          path: path,
          node: JSON.stringify(node, null, 2).substring(0, 300) + '...'
        });
      }
      
      // 递归访问子节点
      for (const [key, value] of Object.entries(node)) {
        if (Array.isArray(value)) {
          value.forEach((child, index) => {
            visitNode(child, `${path}.${key}[${index}]`);
          });
        } else if (value && typeof value === 'object') {
          visitNode(value, `${path}.${key}`);
        }
      }
    }
    
    visitNode(ast);
    
    console.log(`📊 在span范围 ${targetSpanRange.start}-${targetSpanRange.end} 找到 ${foundNodes.length} 个节点:\n`);
    
    foundNodes.forEach((nodeInfo, index) => {
      console.log(`${index + 1}. 类型: ${nodeInfo.type}`);
      console.log(`   Span: ${nodeInfo.span.start}-${nodeInfo.span.end}`);
      console.log(`   路径: ${nodeInfo.path}`);
      
      // 显示实际的源码内容
      const actualContent = sourceCode.slice(nodeInfo.span.start, nodeInfo.span.end);
      console.log(`   内容: "${actualContent}"`);
      console.log();
    });
    
    // 特别检查span 6799附近的内容
    console.log('🎯 特别检查span 6799:');
    const span6799 = 6799;
    const context = sourceCode.slice(span6799 - 20, span6799 + 20);
    console.log(`前后文: "${context}"`);
    console.log(`字符@6799: "${sourceCode[span6799]}"`);
    
    // 检查这个位置是否真的被识别为IfStatement
    foundNodes.forEach((nodeInfo) => {
      if (nodeInfo.span.start <= span6799 && nodeInfo.span.end >= span6799) {
        console.log(`包含span 6799的节点: ${nodeInfo.type} (${nodeInfo.span.start}-${nodeInfo.span.end})`);
      }
    });
    
  } catch (error) {
    console.error('调试失败:', error);
  }
}

debugSwcAst();