import { analyzeCode } from './src/index';

const originalProblemCode = `
function handleExport() {
  fireExport({
    exportParams: {
      bizType,
      jobName: 'NDR',
    }
  });
}
`;

async function testOriginalProblem() {
  console.log('🔍 测试原始问题代码...\n');
  
  try {
    const result = await analyzeCode(originalProblemCode, 'original-problem.tsx', {
      enableDebugLog: true,
      includeDetails: true,
      enableShowContext: true
    });
    
    console.log('\n📊 分析结果:');
    console.log(`总复杂度: ${result.complexity}`);
    console.log(`函数数量: ${result.functions.length}`);
    
    if (result.functions.length > 0) {
      result.functions.forEach(func => {
        console.log(`\n🔧 函数: ${func.name}`);
        console.log(`  复杂度: ${func.complexity}`);
        console.log(`  位置: L${func.line}:C${func.column}`);
        
        if (func.details && func.details.length > 0) {
          console.log('  详细步骤:');
          func.details.forEach((detail, index) => {
            console.log(`    ${index + 1}. L${detail.line}:C${detail.column} +${detail.increment} - ${detail.description} (${detail.ruleId})`);
            if (detail.nodeType) {
              console.log(`       节点类型: ${detail.nodeType}`);
            }
            if (detail.diagnosticMessage) {
              console.log(`       诊断: ${detail.diagnosticMessage}`);
            }
          });
        } else {
          console.log('  无详细步骤记录');
        }
      });
    }
    
    // 验证是否还有span定位错误
    console.log('\n🎯 验证结果:');
    if (result.complexity === 0) {
      console.log('✅ 复杂度为0，没有发现误判的复杂度贡献');
    } else {
      console.log('❌ 仍然存在复杂度贡献，需要进一步检查');
    }
    
  } catch (error) {
    console.error('❌ 分析失败:', error);
  }
}

testOriginalProblem();