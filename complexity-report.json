{"summary": {"totalComplexity": 4, "averageComplexity": 1.3333333333333333, "filesAnalyzed": 1, "functionsAnalyzed": 3, "highComplexityFunctions": 0}, "results": [{"filePath": "/var/folders/sf/rnr3jjbn3qb0c43yyjl3p9900000gn/T/cognitive-TCbA4Q/test-mixed-logic.ts", "complexity": 4, "functions": [{"name": "mixedLogicExample", "complexity": 2, "line": 2, "column": 8, "filePath": "/var/folders/sf/rnr3jjbn3qb0c43yyjl3p9900000gn/T/cognitive-TCbA4Q/test-mixed-logic.ts", "details": []}, {"name": "pureAndExample", "complexity": 2, "line": 8, "column": 8, "filePath": "/var/folders/sf/rnr3jjbn3qb0c43yyjl3p9900000gn/T/cognitive-TCbA4Q/test-mixed-logic.ts", "details": []}, {"name": "defaultValueExample", "complexity": 0, "line": 14, "column": 8, "filePath": "/var/folders/sf/rnr3jjbn3qb0c43yyjl3p9900000gn/T/cognitive-TCbA4Q/test-mixed-logic.ts", "details": []}], "averageComplexity": 1.3333333333333333}], "filterStatistics": {"totalFiles": 1, "displayedFiles": 1, "hiddenFiles": 0, "threshold": 1, "hasFiltering": false, "filterReason": "未应用文件级过滤", "hiddenFilesAvgComplexity": 0, "displayedFilesAvgComplexity": 4}, "metadata": {"schemaVersion": "2.1.0", "generatedAt": "2025-07-31T08:45:07.876Z", "format": "cognitive-complexity-json", "detailsEnabled": true, "contextEnabled": false, "contextAllEnabled": false, "errorRecoveryEnabled": true}}