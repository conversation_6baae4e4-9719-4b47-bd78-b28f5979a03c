import { analyzeCode } from './src/index';

const complexCode = `
function complexFunction(x) {
  if (x > 0) {          // +1 复杂度
    while (x > 10) {    // +1 复杂度
      x--;
    }
    
    try {
      doSomething(x);
    } catch (error) {    // +1 复杂度
      console.error(error);
    }
  }
  
  return x > 5 ? 'high' : 'low';  // +1 复杂度 (三元操作符)
}
`;

async function testComplexFunction() {
  console.log('🔍 测试复杂函数的复杂度计算...\n');
  
  try {
    const result = await analyzeCode(complexCode, 'complex-test.js', {
      enableDebugLog: false, // 关闭调试日志使输出更清晰
      includeDetails: true,
      enableShowContext: true
    });
    
    console.log('📊 分析结果:');
    console.log(`总复杂度: ${result.complexity}`);
    console.log(`函数数量: ${result.functions.length}`);
    
    if (result.functions.length > 0) {
      result.functions.forEach(func => {
        console.log(`\n🔧 函数: ${func.name}`);
        console.log(`  复杂度: ${func.complexity}`);
        console.log(`  位置: L${func.line}:C${func.column}`);
        
        if (func.details && func.details.length > 0) {
          console.log('  详细步骤:');
          func.details.forEach((detail, index) => {
            console.log(`    ${index + 1}. L${detail.line}:C${detail.column} +${detail.increment} - ${detail.description} (${detail.ruleId})`);
            if (detail.nodeType) {
              console.log(`       节点类型: ${detail.nodeType}`);
            }
          });
        }
      });
    }
    
    console.log('\n🎯 验证结果:');
    if (result.complexity === 4) {
      console.log('✅ 复杂度计算正确 (if + while + catch + 三元操作符 = 4)');
    } else {
      console.log(`❌ 复杂度计算异常，期望4，实际${result.complexity}`);
    }
    
  } catch (error) {
    console.error('❌ 分析失败:', error);
  }
}

testComplexFunction();