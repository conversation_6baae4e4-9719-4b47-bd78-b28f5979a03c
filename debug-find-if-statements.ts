/**
 * 找到真正的if语句位置
 */

import * as fs from 'fs';

function findIfStatements() {
  console.log('🔍 查找真正的if语句位置...\n');
  
  try {
    const filePath = '/Users/<USER>/projj/git.imile.com/ux/ds-web/apps/ds/src/pages/InventoryEE/NDR/index.tsx';
    const sourceCode = fs.readFileSync(filePath, 'utf-8');
    const lines = sourceCode.split('\n');
    
    // 查找包含if语句的行
    lines.forEach((line, index) => {
      if (line.trim().startsWith('if (')) {
        console.log(`L${index + 1}: "${line}"`);
        
        // 计算这一行的字节偏移量
        let offset = 0;
        for (let i = 0; i < index; i++) {
          offset += lines[i].length + 1; // +1 for newline
        }
        console.log(`  字节偏移量: ${offset}`);
        console.log();
      }
    });
    
    // 检查span 6799周围的实际内容
    console.log('📍 Span 6799周围内容:');
    const span = 6799;
    const context = sourceCode.slice(span - 50, span + 50);
    console.log(`"${context}"`);
    
    // 手动计算span 6799应该在哪一行
    let currentOffset = 0;
    let lineNumber = 1;
    for (let i = 0; i < sourceCode.length && currentOffset < span; i++) {
      if (sourceCode[i] === '\n') {
        lineNumber++;
      }
      currentOffset++;
    }
    console.log(`\nSpan 6799手动计算应该在: L${lineNumber}`);
    
  } catch (error) {
    console.error('查找失败:', error);
  }
}

findIfStatements();