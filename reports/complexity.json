{"summary": {"totalComplexity": 38, "averageComplexity": 7.6, "filesAnalyzed": 3, "functionsAnalyzed": 5, "highComplexityFunctions": 1}, "results": [{"filePath": "/Users/<USER>/Documents/code/personal/cognitive-complexity/test-e2e-scan-exclude/packages/ui/src/components/Complex.tsx", "complexity": 16, "functions": [{"name": "getClassName", "complexity": 16, "line": 35, "column": 9, "filePath": "/Users/<USER>/Documents/code/personal/cognitive-complexity/test-e2e-scan-exclude/packages/ui/src/components/Complex.tsx", "details": [{"line": 3, "column": 1, "increment": 2, "cumulative": 2, "ruleId": "条件语句", "description": "条件语句", "nestingLevel": 1, "context": "条件语句 (嵌套层级: 1)", "diagnosticMarker": "unknown", "diagnosticMessage": "未知规则标识符: 条件语句 (节点类型: IfStatement) (span: 1696-1968) 在 L3:C1"}, {"line": 3, "column": 1, "increment": 3, "cumulative": 5, "ruleId": "条件语句", "description": "条件语句", "nestingLevel": 1, "context": "条件语句 (嵌套层级: 2)", "diagnosticMarker": "unknown", "diagnosticMessage": "未知规则标识符: 条件语句 (节点类型: IfStatement) (span: 1793-1968) 在 L3:C1"}, {"line": 3, "column": 1, "increment": 4, "cumulative": 9, "ruleId": "条件语句", "description": "条件语句", "nestingLevel": 1, "context": "条件语句 (嵌套层级: 3)", "diagnosticMarker": "unknown", "diagnosticMessage": "未知规则标识符: 条件语句 (节点类型: IfStatement) (span: 1885-1968) 在 L3:C1"}, {"line": 3, "column": 1, "increment": 2, "cumulative": 11, "ruleId": "条件语句", "description": "条件语句", "nestingLevel": 1, "context": "条件语句 (嵌套层级: 1)", "diagnosticMarker": "unknown", "diagnosticMessage": "未知规则标识符: 条件语句 (节点类型: IfStatement) (span: 1978-2149) 在 L3:C1"}, {"line": 3, "column": 1, "increment": 3, "cumulative": 14, "ruleId": "条件语句", "description": "条件语句", "nestingLevel": 1, "context": "条件语句 (嵌套层级: 2)", "diagnosticMarker": "unknown", "diagnosticMessage": "未知规则标识符: 条件语句 (节点类型: IfStatement) (span: 2070-2149) 在 L3:C1"}, {"line": 3, "column": 1, "increment": 2, "cumulative": 16, "ruleId": "条件语句", "description": "条件语句", "nestingLevel": 1, "context": "条件语句 (嵌套层级: 1)", "diagnosticMarker": "unknown", "diagnosticMessage": "未知规则标识符: 条件语句 (节点类型: IfStatement) (span: 2159-2251) 在 L3:C1"}], "severity": "Warning"}, {"name": "Complex", "complexity": 0, "line": 35, "column": 9, "filePath": "/Users/<USER>/Documents/code/personal/cognitive-complexity/test-e2e-scan-exclude/packages/ui/src/components/Complex.tsx", "details": []}], "averageComplexity": 8}, {"filePath": "/Users/<USER>/Documents/code/personal/cognitive-complexity/test-e2e-scan-exclude/apps/web/src/pages/dashboard.tsx", "complexity": 13, "functions": [{"name": "fetchData", "complexity": 13, "line": 6, "column": 22, "filePath": "/Users/<USER>/Documents/code/personal/cognitive-complexity/test-e2e-scan-exclude/apps/web/src/pages/dashboard.tsx", "details": [{"line": 11, "column": 7, "increment": 3, "cumulative": 3, "ruleId": "条件语句", "description": "条件语句", "nestingLevel": 1, "context": "条件语句 (嵌套层级: 2)", "diagnosticMarker": "unknown", "diagnosticMessage": "未知规则标识符: 条件语句 (节点类型: IfStatement) (span: 300-786) 在 L11:C7"}, {"line": 12, "column": 9, "increment": 4, "cumulative": 7, "ruleId": "条件语句", "description": "条件语句", "nestingLevel": 1, "context": "条件语句 (嵌套层级: 3)", "diagnosticMarker": "unknown", "diagnosticMessage": "未知规则标识符: 条件语句 (节点类型: IfStatement) (span: 366-581) 在 L12:C9"}, {"line": 17, "column": 14, "increment": 4, "cumulative": 11, "ruleId": "条件语句", "description": "条件语句", "nestingLevel": 1, "context": "条件语句 (嵌套层级: 3)", "diagnosticMarker": "unknown", "diagnosticMessage": "未知规则标识符: 条件语句 (节点类型: IfStatement) (span: 595-786) 在 L17:C14"}, {"line": 22, "column": 36, "increment": 2, "cumulative": 13, "ruleId": "catch 异常处理", "description": "catch 异常处理", "nestingLevel": 1, "context": "catch 异常处理 (嵌套层级: 1)", "diagnosticMarker": "unknown", "diagnosticMessage": "未知规则标识符: catch 异常处理 (节点类型: CatchClause) (span: 793-876) 在 L22:C36"}], "severity": "Warning"}, {"name": "Dashboard", "complexity": 0, "line": 2, "column": 8, "filePath": "/Users/<USER>/Documents/code/personal/cognitive-complexity/test-e2e-scan-exclude/apps/web/src/pages/dashboard.tsx", "details": []}], "averageComplexity": 6.5}, {"filePath": "/Users/<USER>/Documents/code/personal/cognitive-complexity/test-e2e-scan-exclude/packages/core/src/index.ts", "complexity": 9, "functions": [{"name": "coreFunction", "complexity": 9, "line": 2, "column": 8, "filePath": "/Users/<USER>/Documents/code/personal/cognitive-complexity/test-e2e-scan-exclude/packages/core/src/index.ts", "details": [{"line": 2, "column": 1, "increment": 2, "cumulative": 2, "ruleId": "条件语句", "description": "条件语句", "nestingLevel": 0, "context": "条件语句 (嵌套层级: 1)", "diagnosticMarker": "unknown", "diagnosticMessage": "未知规则标识符: 条件语句 (节点类型: IfStatement) (span: 1059-1423) 在 L2:C1"}, {"line": 2, "column": 1, "increment": 3, "cumulative": 5, "ruleId": "条件语句", "description": "条件语句", "nestingLevel": 0, "context": "条件语句 (嵌套层级: 2)", "diagnosticMarker": "unknown", "diagnosticMessage": "未知规则标识符: 条件语句 (节点类型: IfStatement) (span: 1151-1423) 在 L2:C1"}, {"line": 2, "column": 1, "increment": 4, "cumulative": 9, "ruleId": "条件语句", "description": "条件语句", "nestingLevel": 0, "context": "条件语句 (嵌套层级: 3)", "diagnosticMarker": "unknown", "diagnosticMessage": "未知规则标识符: 条件语句 (节点类型: IfStatement) (span: 1208-1302) 在 L2:C1"}]}], "averageComplexity": 9}], "filterStatistics": {"totalFiles": 3, "displayedFiles": 3, "hiddenFiles": 0, "threshold": 1, "hasFiltering": false, "filterReason": "未应用文件级过滤", "hiddenFilesAvgComplexity": 0, "displayedFilesAvgComplexity": 12.666666666666666}, "metadata": {"schemaVersion": "2.1.0", "generatedAt": "2025-07-31T08:45:10.694Z", "format": "cognitive-complexity-json", "detailsEnabled": true, "contextEnabled": false, "contextAllEnabled": false, "errorRecoveryEnabled": true}, "diagnostics": {"totalWarnings": 0, "totalErrors": 0, "totalUnknown": 13, "hasIssues": true}}