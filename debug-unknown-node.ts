import { analyzeCode } from './src/index';

const testCode = `
function handleExport() {
  fireExport({
    exportParams: {
      bizType,
      jobName: 'NDR',
    }
  });
}
`;

async function debugUnknownNode() {
  console.log('🔍 开始调试未知节点问题...\n');
  
  try {
    const result = await analyzeCode(testCode, 'debug-test.tsx', {
      enableDebugLog: true,
      includeDetails: true,
      enableShowContext: true
    });
    
    console.log('\n📊 分析结果:');
    console.log(`总复杂度: ${result.complexity}`);
    console.log(`函数数量: ${result.functions.length}`);
    
    result.functions.forEach(func => {
      console.log(`\n🔧 函数: ${func.name}`);
      console.log(`  复杂度: ${func.complexity}`);
      console.log(`  位置: L${func.line}:C${func.column}`);
      
      if (func.details && func.details.length > 0) {
        console.log('  详细步骤:');
        func.details.forEach((detail, index) => {
          console.log(`    ${index + 1}. L${detail.line}:C${detail.column} +${detail.increment} - ${detail.description}`);
          if (detail.nodeType) {
            console.log(`       节点类型: ${detail.nodeType}`);
          }
        });
      }
    });
    
  } catch (error) {
    console.error('❌ 分析失败:', error);
  }
}

debugUnknownNode();